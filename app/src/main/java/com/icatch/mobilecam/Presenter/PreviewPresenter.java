package com.icatch.mobilecam.Presenter;

import static com.icatch.mobilecam.Application.PanoramaApp.getContext;
import static com.icatch.mobilecam.Application.PanoramaApp.getInstance;
import static com.icatch.mobilecam.data.Mode.PreviewMode.APP_STATE_TIMELAPSE_VIDEO_PREVIEW;
import static razerdp.basepopup.BasePopupSDK.getApplication;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ActivityInfo;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.media.MediaPlayer;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Environment;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.SurfaceHolder;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.GsonUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.SPUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.detu.szStitch.StitchUtils;
import com.icatch.mobilecam.Application.PanoramaApp;
import com.icatch.mobilecam.DataConvert.StreamInfoConvert;
import com.icatch.mobilecam.Function.BaseProrertys;
import com.icatch.mobilecam.Function.CameraAction.PbDownloadManager;
import com.icatch.mobilecam.Function.CameraAction.PhotoCapture;
import com.icatch.mobilecam.Function.CameraAction.ZoomInOut;
import com.icatch.mobilecam.Function.SDKEvent;
import com.icatch.mobilecam.Function.Setting.OptionSetting;
import com.icatch.mobilecam.Function.Setting.UIDisplaySource;
import com.icatch.mobilecam.Function.ThumbnailGetting.ThumbnailOperation;
import com.icatch.mobilecam.Function.streaming.CameraStreaming;
import com.icatch.mobilecam.Listener.OnSettingCompleteListener;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.MyCamera.CameraManager;
import com.icatch.mobilecam.MyCamera.CameraType;
import com.icatch.mobilecam.MyCamera.MyCamera;
import com.icatch.mobilecam.Presenter.Interface.BasePresenter;
import com.icatch.mobilecam.SdkApi.CameraAction;
import com.icatch.mobilecam.SdkApi.CameraProperties;
import com.icatch.mobilecam.SdkApi.CameraState;
import com.icatch.mobilecam.SdkApi.FileOperation;
import com.icatch.mobilecam.SdkApi.PanoramaPreviewPlayback;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.data.CustomException.NullPointerException;
import com.icatch.mobilecam.data.GlobalApp.GlobalInfo;
import com.icatch.mobilecam.data.Message.AppMessage;
import com.icatch.mobilecam.data.Mode.PreviewMode;
import com.icatch.mobilecam.data.Mode.TouchMode;
import com.icatch.mobilecam.data.PropertyId.PropertyId;
import com.icatch.mobilecam.data.entity.CameraSlot;
import com.icatch.mobilecam.data.entity.SettingMenu;
import com.icatch.mobilecam.data.entity.StreamInfo;
import com.icatch.mobilecam.data.type.A6MaxRecordStatus;
import com.icatch.mobilecam.data.type.A6RotateMotorState;
import com.icatch.mobilecam.data.type.A6RotateShotTimes;
import com.icatch.mobilecam.data.type.SlowMotion;
import com.icatch.mobilecam.data.type.TimeLapseInterval;
import com.icatch.mobilecam.data.type.TimeLapseMode;
import com.icatch.mobilecam.data.type.Tristate;
import com.icatch.mobilecam.data.type.Upside;
import com.icatch.mobilecam.db.CameraSlotSQLite;
import com.icatch.mobilecam.evenbus.BackgroundStitchingCountRefreshEvent;
import com.icatch.mobilecam.evenbus.EventBusNotifyEntity;
import com.icatch.mobilecam.evenbus.RemarkClearEvent;
import com.icatch.mobilecam.ui.ExtendComponent.MyProgressDialog;
import com.icatch.mobilecam.ui.ExtendComponent.MyToast;
import com.icatch.mobilecam.ui.Interface.PreviewView;
import com.icatch.mobilecam.ui.activity.PreviewActivity;
import com.icatch.mobilecam.ui.adapter.SettingListAdapter;
import com.icatch.mobilecam.ui.appdialog.AppDialog;
import com.icatch.mobilecam.ui.appdialog.AppToast;
import com.icatch.mobilecam.ui.popupwindow.FileListPopupWindow;
import com.icatch.mobilecam.utils.BitmapTools;
import com.icatch.mobilecam.utils.CameraUtils;
import com.icatch.mobilecam.utils.ConvertTools;
import com.icatch.mobilecam.utils.MediaRefresh;
import com.icatch.mobilecam.utils.PanoramaTools;
import com.icatch.mobilecam.utils.QRCode;
import com.icatch.mobilecam.utils.SPKey;
import com.icatch.mobilecam.utils.StorageUtil;
import com.icatch.mobilecam.utils.ToastUtil;
import com.icatch.mobilecam.utils.executor.AppExecutors;
import com.icatch.mobilecam.utils.fileutils.FileOper;
import com.icatch.mobilecam.utils.fileutils.FileTools;
import com.icatchtek.control.customer.type.ICatchCamDateStamp;
import com.icatchtek.control.customer.type.ICatchCamEventID;
import com.icatchtek.control.customer.type.ICatchCamMode;
import com.icatchtek.control.customer.type.ICatchCamPreviewMode;
import com.icatchtek.control.customer.type.ICatchCamProperty;
import com.icatchtek.pancam.customer.ICatchPancamConfig;
import com.icatchtek.pancam.customer.exception.IchGLSurfaceNotSetException;
import com.icatchtek.pancam.customer.surface.ICatchSurfaceContext;
import com.icatchtek.pancam.customer.type.ICatchGLPanoramaType;
import com.icatchtek.pancam.customer.type.ICatchGLPoint;
import com.icatchtek.pancam.customer.type.ICatchGLSurfaceType;
import com.icatchtek.reliant.customer.type.ICatchFile;
import com.icatchtek.reliant.customer.type.ICatchH264StreamParam;
import com.icatchtek.reliant.customer.type.ICatchJPEGStreamParam;
import com.icatchtek.reliant.customer.type.ICatchStreamParam;
import com.ijoyer.camera.bean.HdrBean;
import com.ijoyer.camera.utils.LogUtil;
import com.ijoyer.mobilecam.BuildConfig;
import com.ijoyer.mobilecam.R;
import com.tencent.mmkv.MMKV;

import org.greenrobot.eventbus.EventBus;
import org.opencv.android.BaseLoaderCallback;
import org.opencv.android.LoaderCallbackInterface;
import org.opencv.android.OpenCVLoader;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Pattern;

public class PreviewPresenter extends BasePresenter implements SensorEventListener {
    private static final String TAG = "PanoramaPreviewPresenter";
    private final static float MIN_ZOOM = 0.4f;
    private final static float MAX_ZOOM = 2.2f;
    private PanoramaPreviewPlayback panoramaPreviewPlayback;
    private TouchMode touchMode = TouchMode.NONE;
    private float mPreviousY;
    private float mPreviousX;
    private float beforeLenght;
    private float afterLenght;
    private float currentZoomRate = MAX_ZOOM;
    private SensorManager sensorManager;
    private Sensor gyroscopeSensor;
    private MediaPlayer videoCaptureStartBeep;
    private MediaPlayer modeSwitchBeep;
    private MediaPlayer stillCaptureStartBeep;
    private MediaPlayer continuousCaptureBeep;
    private Activity activity;
    private PreviewView previewView;
    private CameraProperties cameraProperties;
    private CameraAction cameraAction;
    private CameraState cameraState;
    private FileOperation fileOperation;
    private BaseProrertys baseProrertys;
    private MyCamera curCamera;
    private PreviewHandler previewHandler;
    private SDKEvent sdkEvent;
    private int curAppStateMode = PreviewMode.APP_STATE_NONE_MODE;
    private Timer videoCaptureButtomChangeTimer;
    public boolean videoCaptureButtomChangeFlag = true;
    private Timer recordingLapseTimeTimer;
    private int lapseTime = 0;
    private List<SettingMenu> settingMenuList;
    private SettingListAdapter settingListAdapter;
    private boolean allowClickButtoms = true;
    private int currentSettingMenuMode;
    private WifiSSReceiver wifiSSReceiver;
    private long lastCilckTime = 0;
    private long lastRecodeTime;
    private int curICatchMode;
    private ICatchSurfaceContext iCatchSurfaceContext;
    private boolean hasInitSurface = false;
    private ZoomInOut zoomInOut;
    private int curVideoWidth = 1920;
    private int curVideoHeight = 960;
    private int curVideoFps = 30;
    private String curCodecType = "H264";
    private CameraStreaming cameraStreaming;
    private int curPanoramaType = ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_SPHERE;
    boolean isDelEvent = false;
    public static boolean isA6Camera = false;
    private boolean isA6MaxCamera = false;
    private FileListPopupWindow fileListPopupWindow = null;
    //收到的文件列表，旧逻辑，单线程使用
    private ArrayList<ICatchFile> iCatchFiles;
    private PbDownloadManager pbDownloadManager;
    //新收到的文件列表，<groupName,文件列表>，多线程HDR 的时候会使用
    private final Map<String, List<ICatchFile>> reciverFileMap = new HashMap<>();
    //已经下载的文件列表
    private final Map<String, ArrayList<String>> successDownloadMap = new HashMap<>();
    private boolean isDownloadingFile = false;
    /**
     * 本次拍摄时输入的备注名
     */
    public String currentRemarkName;
    /**
     * 拼接中的文件组名
     */
    public static CopyOnWriteArraySet<String> stitchingGroupList = new CopyOnWriteArraySet<>();

    private BaseLoaderCallback mLoaderCallback = new BaseLoaderCallback(getContext()) {
        @Override
        public void onManagerConnected(int status) {
            switch (status) {
                case LoaderCallbackInterface.SUCCESS: {
                    LogUtil.e("OpenCV loaded successfully");
                }
                break;
                default: {
                    super.onManagerConnected(status);
                }
                break;
            }
        }
    };

    private ScheduledExecutorService a6CaptureScheduler;
    private static ScheduledFuture<?> a6CaptureTask;  // 静态的任务状态，用于在下次时取消任务

    //A6自动连续拍摄
    public static boolean openAutoCaptureWithA6;

    // A6Max录像处理相关变量
    private boolean isA6MaxRecordProcessing = false;
    private Timer a6MaxRecordMonitorTimer;



    public PreviewPresenter(Activity activity) {
        super(activity);
        this.activity = activity;
        pbDownloadManager = new PbDownloadManager(activity);
        //A6的定时器，优先级设置到最高，防止被回收
        a6CaptureScheduler = Executors.newScheduledThreadPool(1, runnable -> {
            Thread thread = new Thread(runnable);
            thread.setPriority(Thread.MAX_PRIORITY); // 设置线程优先级为最高
            return thread;
        });

        if (!OpenCVLoader.initDebug()) {
            OpenCVLoader.initAsync(OpenCVLoader.OPENCV_VERSION, getContext(), mLoaderCallback);
        }

    }

    public void setView(PreviewView previewView) {
        this.previewView = previewView;
        initCfg();
        initData();
    }

    public void initData() {
        curCamera = CameraManager.getInstance().getCurCamera();
        panoramaPreviewPlayback = curCamera.getPanoramaPreviewPlayback();
        cameraStreaming = new CameraStreaming(panoramaPreviewPlayback);
        cameraProperties = curCamera.getCameraProperties();
        cameraAction = curCamera.getCameraAction();
        cameraState = curCamera.getCameraState();
        fileOperation = curCamera.getFileOperation();
        baseProrertys = curCamera.getBaseProrertys();
        zoomInOut = new ZoomInOut();
        videoCaptureStartBeep = MediaPlayer.create(activity, R.raw.camera_timer);
        stillCaptureStartBeep = MediaPlayer.create(activity, R.raw.captureshutter);
        continuousCaptureBeep = MediaPlayer.create(activity, R.raw.captureburst);
        modeSwitchBeep = MediaPlayer.create(activity, R.raw.focusbeep);
        previewHandler = new PreviewHandler();
        sdkEvent = new SDKEvent(previewHandler);
        if (cameraProperties.hasFunction(PropertyId.CAPTURE_DELAY_MODE)) {
            int delay = cameraProperties.getCurrentPropertyValue(PropertyId.CAPTURE_DELAY_MODE);
            AppLog.i(TAG, "PropertyId.CAPTURE_DELAY_MODE, value = " + delay);
            cameraProperties.setCaptureDelayMode(1);
            AppLog.i(TAG, "PropertyId.CAPTURE_DELAY_MODE, value = " + delay);
        }
        if (curCamera.getCameraType() == CameraType.USB_CAMERA) {
            Intent intent = activity.getIntent();
            curVideoWidth = intent.getIntExtra("videoWidth", 1920);
            curVideoHeight = intent.getIntExtra("videoHeight", 960);
            curVideoFps = intent.getIntExtra("videoFps", 30);
            curCodecType = intent.getStringExtra("videoCodec");
            if (curCodecType == null) {
                curCodecType = "H264";
            }
            AppLog.d(TAG, "initData videoWidth=" + curVideoWidth + " videoHeight=" + curVideoHeight + " videoFps=" + curVideoFps + " curCodecType=" + curCodecType);
        }
        AppLog.i(TAG, "cameraProperties.getMaxZoomRatio() =" + cameraProperties.getMaxZoomRatio());
        isA6Camera = cameraProperties.hasFunction(PropertyId.A6_ROTATE_MOTOR_STATE) && cameraProperties.hasFunction(PropertyId.A6_ROTATE_SHOT_TIMES);
        isA6MaxCamera = CameraUtils.isA6Max();
    }


    public void initStatus() {
        // 清理可能残留的拼接状态
        clearAllStitchingStates();
        refreshBatteryLevel();
        refreshBatteryLevel();
        Boolean isSDCardExist = cameraProperties.isSDCardExist();
        if (Boolean.TRUE.equals(isSDCardExist)) {
            IntentFilter wifiSSFilter = new IntentFilter(WifiManager.RSSI_CHANGED_ACTION);
            wifiSSReceiver = new WifiSSReceiver();
            activity.registerReceiver(wifiSSReceiver, wifiSSFilter);
        } else if (Boolean.FALSE.equals(isSDCardExist)) {
            if (ActivityUtils.getTopActivity() instanceof PreviewActivity) {
                AppDialog.showDialogWarn(activity, R.string.dialog_card_lose);
            }
        } else {
            if (ActivityUtils.getTopActivity() instanceof PreviewActivity) {
                AppDialog.showDialogWarn(activity, R.string.dialog_connect_failed, false, () -> {
                    if (activity != null && !activity.isFinishing()) {
                        activity.finish();
                    }
                });
            }
        }
    }

    // 刷新A6Max 录像模式
    void refreshA6MaxRecordMode() {
        // 直接判断当前的具体状态是不是视频预览或视频录制
        boolean isVideoState = curAppStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW
                || curAppStateMode == PreviewMode.APP_STATE_VIDEO_CAPTURE;

        if (isA6MaxCamera && isVideoState) {
            int a6MaxRecordMode = cameraProperties.getCurrentPropertyValue(PropertyId.A6MAX_RECORD_MODE);
            LogUtils.file("获取A6Max 录像模式：" + a6MaxRecordMode);
            if (a6MaxRecordMode == -1) {
                previewHandler.post(() -> previewView.setA6MaxRecordMode(null));
            } else {
                previewHandler.post(() ->
                        previewView.setA6MaxRecordMode(a6MaxRecordMode == A6RotateMotorState.RECORD_MODE_LONG_VIDEO));
            }
        } else {
            previewHandler.post(() -> previewView.setA6MaxRecordMode(null));
        }
    }

    /**
     * 检查并恢复A6Max录制状态
     * 用于解决app异常退出后重新进入时无法控制录制的问题
     */
    private void checkAndRecoverA6MaxRecordState() {
        // 只在A6Max相机下执行
        if (!isA6MaxCamera) {
            return;
        }

        try {
            LogUtils.d(TAG, "A6Max录制状态恢复：开始检查录制状态");
            LogUtils.file("A6Max录制状态恢复：开始检查录制状态");

            // 检查是否支持A6Max录像状态属性
            if (!cameraProperties.hasFunction(PropertyId.A6MAX_RECORD_STATUS)) {
                LogUtils.w(TAG, "A6Max录制状态恢复：不支持录像状态检查");
                LogUtils.file("A6Max录制状态恢复：不支持录像状态检查");
                return;
            }

            // 获取A6Max录像状态
            int recordStatus = getCurrentPropertyValue(PropertyId.A6MAX_RECORD_STATUS);
            LogUtils.d(TAG, "A6Max录制状态恢复：当前状态 - " + recordStatus + " (" + A6MaxRecordStatus.getStatusDescription(recordStatus) + ")");
            LogUtils.file("A6Max录制状态恢复：当前状态 - " + recordStatus + " (" + A6MaxRecordStatus.getStatusDescription(recordStatus) + ")");

            // 如果相机正在录制，恢复app的录制状态控制
            if (recordStatus == A6MaxRecordStatus.RECORDING_AND_MOVING) {
                LogUtils.i(TAG, "A6Max录制状态恢复：检测到相机正在录制，开始恢复录制状态控制");
                LogUtils.file("A6Max录制状态恢复：检测到相机正在录制，开始恢复录制状态控制");

                recoverA6MaxRecordControl();
            } else {
                LogUtils.d(TAG, "A6Max录制状态恢复：相机未在录制状态，无需恢复");
                LogUtils.file("A6Max录制状态恢复：相机未在录制状态，无需恢复");
            }

        } catch (Exception e) {
            LogUtils.e(TAG, "A6Max录制状态恢复：检查异常", e);
            LogUtils.file("A6Max录制状态恢复：检查异常 - " + e.getMessage());
        }
    }

    /**
     * 恢复A6Max录制控制
     * 复用现有的录制状态恢复逻辑
     */
    private void recoverA6MaxRecordControl() {
        try {
            LogUtils.i(TAG, "A6Max录制状态恢复：开始恢复录制控制");
            LogUtils.file("A6Max录制状态恢复：开始恢复录制控制");

            previewHandler.post(() -> {
                // 复用现有的录制状态恢复逻辑（参考第710-715行的逻辑）
                AppLog.i(TAG, "A6Max camera is recording...");
                curAppStateMode = PreviewMode.APP_STATE_VIDEO_CAPTURE;
                curICatchMode = ICatchCamPreviewMode.ICH_CAM_VIDEO_PREVIEW_MODE;

                // 恢复A6Max录制状态标志
                isA6MaxRecordProcessing = true;

                // 启动录制相关的定时器
                startVideoCaptureButtomChangeTimer();
                startRecordingLapseTimeTimer(0); // A6Max从0开始计时

                // 启动A6Max录制监控
                startA6MaxRecordMonitor();

                // 更新UI状态
                createUIByMode(curAppStateMode);

                LogUtils.i(TAG, "A6Max录制状态恢复：录制控制恢复完成");
                LogUtils.file("A6Max录制状态恢复：录制控制恢复完成");
            });

        } catch (Exception e) {
            LogUtils.e(TAG, "A6Max录制状态恢复：恢复控制异常", e);
            LogUtils.file("A6Max录制状态恢复：恢复控制异常 - " + e.getMessage());

            // 如果恢复失败，重置状态
            previewHandler.post(() -> {
                resetA6MaxRecordState();
            });
        }
    }

    //刷新电量
    private void refreshBatteryLevel() {
        int batteryLevel = cameraProperties.getBatteryElectric();
        int resId = ThumbnailOperation.getBatteryLevelIcon(batteryLevel);
        if (resId > 0) {
            previewView.setBatteryIcon(resId);
            if (batteryLevel < 20) {
                //低电量
            }
        }
        LogUtils.file("刷新电池电量：" + batteryLevel);
    }

    public void changeCameraMode(final int previewMode, final int ichVideoPreviewMode) {
        AppLog.i(TAG, "start changeCameraMode ichVideoPreviewMode=" + ichVideoPreviewMode);
        AppLog.i(TAG, "start changeCameraMode previewMode=" + previewMode + "  hasInitSurface=" + hasInitSurface);
        curICatchMode = ichVideoPreviewMode;
        MyProgressDialog.showProgressDialog(activity, R.string.action_processing);
        new Thread(() -> {
            cameraAction.changePreviewMode(ichVideoPreviewMode);
            startPreview();
            previewHandler.post(() -> {
                curAppStateMode = previewMode;
                createUIByMode(curAppStateMode);
                MyProgressDialog.closeProgressDialog();
                previewView.dismissPopupWindow();
                AppExecutors.runOnIoThread(this::refreshA6MaxRecordMode);
            });
        }).start();
    }

    public void redrawSurface() {
        if (curCamera.isStreamReady && !AppInfo.enableRender) {
            int width = previewView.getSurfaceViewWidth();
            int height = previewView.getSurfaceViewHeight();
            AppLog.i(TAG, "SurfaceViewWidth=" + width + " SurfaceViewHeight=" + height);
            if (width > 0 || height > 0) {
                cameraStreaming.setViewParam(width, height);
                cameraStreaming.setSurfaceViewArea();
            }
        }
    }

    public void startOrStopCapture() {
        LogUtils.file("拍摄：点击拍摄按钮");
        final int duration = videoCaptureStartBeep.getDuration();
        if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW) {
            Boolean isSDCardExist = cameraProperties.isSDCardExist();
            if (isSDCardExist == null) {
                AppDialog.showDialogWarn(activity, R.string.dialog_connect_failed);
                return;
            }
            if (Boolean.FALSE.equals(isSDCardExist)) {
                AppDialog.showDialogWarn(activity, R.string.dialog_card_not_exist);
                return;
            }
            int recordRemainTime = cameraProperties.getRecordingRemainTime();
            if (recordRemainTime <= 0) {
                LogUtils.file(this.getClass().getSimpleName() + "startOrStopCapture-(curAppStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW) getRecordingRemainTime()=" + recordRemainTime);
                String hint;
                if (recordRemainTime < 0) {
                    hint = getInstance().getString(R.string.dialog_connect_lose_exit_page);
                } else {
                    hint = getInstance().getString(R.string.dialog_sd_card_is_full);
                    hint += "(" + recordRemainTime + ")";
                }
                AppDialog.showDialogWarn(activity, hint);
                return;
            }
            MyProgressDialog.showProgressDialog(activity, R.string.action_processing);
            // A6Max和其他相机的录像处理逻辑
            if (isA6MaxCamera) {
                // A6Max专用录像处理流程
                startA6MaxVideoRecord(duration);
            } else {
                // 其他相机的录像处理流程
                new Thread(() -> {
                    videoCaptureStartBeep.start();
                    AppLog.d(TAG, "duration:" + duration);
                    try {
                        Thread.sleep(duration);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    lastRecodeTime = System.currentTimeMillis();
                    final boolean ret = cameraAction.startMovieRecord();
                    previewHandler.post(() -> {
                        MyProgressDialog.closeProgressDialog();
                        if (ret) {
                            AppLog.i(TAG, "startRecordingLapseTimeTimer(0)");
                            curAppStateMode = PreviewMode.APP_STATE_VIDEO_CAPTURE;
                            startVideoCaptureButtomChangeTimer();
                            startRecordingLapseTimeTimer(0);
                        } else {
                            MyToast.show(activity, R.string.text_operation_failed);
                        }
                    });
                }).start();
            }
        } else if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_CAPTURE) {
            if (System.currentTimeMillis() - lastRecodeTime < 2000) {
                return;
            }
            MyProgressDialog.showProgressDialog(activity, R.string.action_processing);

            // A6Max和其他相机的录像停止处理逻辑
            if (isA6MaxCamera) {
                // A6Max专用录像停止处理
                stopA6MaxVideoRecord();
            } else {
                // 其他相机的录像停止处理
                new Thread(() -> {
                    final boolean ret = cameraAction.stopVideoCapture();
                    videoCaptureStartBeep.start();
                    previewHandler.post(() -> {
                        MyProgressDialog.closeProgressDialog();
                        if (ret) {
                            curAppStateMode = PreviewMode.APP_STATE_VIDEO_PREVIEW;
                            stopVideoCaptureButtomChangeTimer();
                            stopRecordingLapseTimeTimer();
                            previewView.setRemainRecordingTimeText(ConvertTools.secondsToMinuteOrHours(cameraProperties.getRecordingRemainTime()));
                        } else {
                            MyToast.show(activity, R.string.text_operation_failed);
                        }
                    });
                }).start();
            }
        } else if (curAppStateMode == PreviewMode.APP_STATE_STILL_PREVIEW) {
            previewView.hideZoomView();
            Boolean isSDCardExist = cameraProperties.isSDCardExist();
            if (isSDCardExist == null) {
                Activity topActivity = ActivityUtils.getTopActivity();
                if (topActivity instanceof PreviewActivity && !topActivity.isFinishing()) {
                    AppDialog.showDialogWarn(activity, R.string.dialog_connect_failed);
                }
                return;
            }
            if (Boolean.FALSE.equals(isSDCardExist)) {
                Activity topActivity = ActivityUtils.getTopActivity();
                if (topActivity instanceof PreviewActivity && !topActivity.isFinishing()) {
                    AppDialog.showDialogWarn(activity, R.string.dialog_card_not_exist);
                }
                return;
            }
            if (!checkRemainImageNum("curAppStateMode == PreviewMode.APP_STATE_STILL_PREVIEW")) {
//                AppDialog.showDialogWarn(activity, R.string.dialog_sd_card_is_full);
                return;
            }
            curAppStateMode = PreviewMode.APP_STATE_STILL_CAPTURE;
            startPhotoCapture();
        } else if (curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW) {
            Boolean isSDCardExist = cameraProperties.isSDCardExist();
            if (isSDCardExist == null) {
                AppDialog.showDialogWarn(activity, R.string.dialog_connect_failed);
                return;
            } else if (Boolean.FALSE.equals(isSDCardExist)) {
                AppDialog.showDialogWarn(activity, R.string.dialog_card_not_exist);
                return;
            }
            if (!checkRemainImageNum("curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW")) {
//                AppDialog.showDialogWarn(activity, R.string.dialog_sd_card_is_full);
                return;
            }
            if (cameraProperties.getCurrentTimeLapseInterval() == TimeLapseInterval.TIME_LAPSE_INTERVAL_OFF) {
                AppDialog.showDialogWarn(activity, R.string.timeLapse_not_allow);
                return;
            }
            continuousCaptureBeep.start();
            if (!cameraAction.startTimeLapse()) {
                AppLog.e(TAG, "failed to start startTimeLapse");
                return;
            }
            previewView.setCaptureBtnBackgroundResource(R.drawable.still_capture_btn_off);
            curAppStateMode = PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE;
        } else if (curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE) {
            AppLog.d(TAG, "curMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE");
            if (cameraAction.stopTimeLapse() == false) {
                AppLog.e(TAG, "failed to stopTimeLapse");
                return;
            }
            stopRecordingLapseTimeTimer();
            curAppStateMode = PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW;
        } else if (curAppStateMode == APP_STATE_TIMELAPSE_VIDEO_PREVIEW) {
            AppLog.d(TAG, "curMode == PreviewMode.APP_STATE_TIMELAPSE_PREVIEW_VIDEO");
            Boolean isSDCardExist = cameraProperties.isSDCardExist();
            if (isSDCardExist == null) {
                AppDialog.showDialogWarn(activity, R.string.dialog_connect_failed);
                return;
            } else if (Boolean.FALSE.equals(isSDCardExist)) {
                AppDialog.showDialogWarn(activity, R.string.dialog_card_not_exist);
                return;
            }
            if (!checkRemainImageNum("curAppStateMode == APP_STATE_TIMELAPSE_VIDEO_PREVIEW")) {
//                AppDialog.showDialogWarn(activity, R.string.dialog_sd_card_is_full);
                return;
            }
            if (cameraProperties.getCurrentTimeLapseInterval() == TimeLapseInterval.TIME_LAPSE_INTERVAL_OFF) {
                AppLog.d(TAG, "time lapse is not allowed because of timelapse interval is OFF");
                AppDialog.showDialogWarn(activity, R.string.timeLapse_not_allow);
                return;
            }
            videoCaptureStartBeep.start();
            if (cameraAction.startTimeLapse() == false) {
                AppLog.e(TAG, "failed to start startTimeLapse");
                return;
            }
            curAppStateMode = PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE;
            startVideoCaptureButtomChangeTimer();
            startRecordingLapseTimeTimer(0);
        } else if (curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE) {
            AppLog.d(TAG, "curMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE");
            videoCaptureStartBeep.start();
            if (cameraAction.stopTimeLapse() == false) {
                AppLog.e(TAG, "failed to stopTimeLapse");
                return;
            }
            stopVideoCaptureButtomChangeTimer();
            stopRecordingLapseTimeTimer();
            curAppStateMode = APP_STATE_TIMELAPSE_VIDEO_PREVIEW;
        }
        AppLog.d(TAG, "end processing for responsing captureBtn clicking");
    }

    //检查剩余可拍摄图片数量
    private boolean checkRemainImageNum(String errLog) {
        int recordRemainTime = cameraProperties.getRemainImageNum();
        if (recordRemainTime < 1) {
            LogUtils.file(this.getClass().getSimpleName() + errLog + " checkRemainImageNum()=" + recordRemainTime);
            String hint;
            if (recordRemainTime < 0) {
                hint = getInstance().getString(R.string.dialog_connect_lose_exit_page);
            } else {
                hint = getInstance().getString(R.string.dialog_sd_card_is_full);
                hint += "(" + recordRemainTime + ")";
            }
            AppDialog.showDialogWarn(activity, hint);
            return false;
        } else {
            return true;
        }
    }

    public void createUIByMode(int appStateMode) {
        AppLog.i(TAG, "start createUIByMode previewMode=" + appStateMode);
        if (cameraProperties.cameraModeSupport(ICatchCamMode.ICH_CAM_MODE_VIDEO)) {
            if (appStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW || appStateMode == PreviewMode.APP_STATE_VIDEO_CAPTURE) {
                previewView.setPvModeBtnBackgroundResource(R.drawable.video_toggle_btn_on);
            }
        }
        if (appStateMode == PreviewMode.APP_STATE_STILL_PREVIEW || appStateMode == PreviewMode.APP_STATE_STILL_CAPTURE) {
            previewView.setPvModeBtnBackgroundResource(R.drawable.capture_toggle_btn_on);
        }
        if (cameraProperties.cameraModeSupport(ICatchCamMode.ICH_CAM_MODE_TIMELAPSE)) {
            if (appStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE || appStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW || appStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE || appStateMode == APP_STATE_TIMELAPSE_VIDEO_PREVIEW) {
                previewView.setPvModeBtnBackgroundResource(R.drawable.timelapse_toggle_btn_on);
            }
        }
        if (isA6Camera && !isA6MaxCamera) {//A6Max 单独处理，有录像功能
            previewView.setPvModeBtnVisibility(View.GONE);
        }
        if (appStateMode == PreviewMode.APP_STATE_STILL_CAPTURE || appStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE || appStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW || appStateMode == PreviewMode.APP_STATE_STILL_PREVIEW) {
            previewView.setCaptureBtnBackgroundResource(R.drawable.still_capture_btn);
        } else if (appStateMode == PreviewMode.APP_STATE_VIDEO_CAPTURE || appStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE || appStateMode == APP_STATE_TIMELAPSE_VIDEO_PREVIEW || appStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW) {
            previewView.setCaptureBtnBackgroundResource(R.drawable.video_recording_btn_on);
        }
        if (baseProrertys.getCaptureDelay().needDisplayByMode(appStateMode) && !isA6Camera) {
            previewView.setDelayCaptureLayoutVisibility(View.VISIBLE);
            previewView.setDelayCaptureTextTime(baseProrertys.getCaptureDelay().getCurrentUiStringInPreview());
        } else {
            previewView.setDelayCaptureLayoutVisibility(View.GONE);
        }
        if (baseProrertys.getImageSize().needDisplayByMode(appStateMode)) {
            previewView.setImageSizeLayoutVisibility(View.VISIBLE);
            previewView.setImageSizeInfo(baseProrertys.getImageSize().getCurrentUiStringInPreview());
            previewView.setRemainCaptureCount(new Integer(cameraProperties.getRemainImageNum()).toString());
        } else {
            previewView.setImageSizeLayoutVisibility(View.GONE);
        }
        if (baseProrertys.getVideoSize().needDisplayByMode(appStateMode)) {
            previewView.setVideoSizeLayoutVisibility(View.VISIBLE);
            previewView.setVideoSizeInfo(baseProrertys.getVideoSize().getCurrentUiStringInPreview());
            previewView.setRemainRecordingTimeText(ConvertTools.secondsToMinuteOrHours(cameraProperties.getRecordingRemainTime()));
        } else {
            previewView.setVideoSizeLayoutVisibility(View.GONE);
        }
        if (baseProrertys.getBurst().needDisplayByMode(appStateMode)) {
            previewView.setBurstStatusVisibility(View.VISIBLE);
            try {
                previewView.setBurstStatusIcon(baseProrertys.getBurst().getCurrentIcon());
            } catch (NullPointerException e) {
                e.printStackTrace();
            }
        } else {
            previewView.setBurstStatusVisibility(View.GONE);
        }
        if (baseProrertys.getWhiteBalance().needDisplayByMode(appStateMode)) {
            previewView.setWbStatusVisibility(View.VISIBLE);
            try {
                previewView.setWbStatusIcon(baseProrertys.getWhiteBalance().getCurrentIcon());
            } catch (NullPointerException e) {
                e.printStackTrace();
            }
        } else {
            previewView.setWbStatusVisibility(View.GONE);
        }
        if (baseProrertys.getUpside().needDisplayByMode(appStateMode) && cameraProperties.getCurrentUpsideDown() == Upside.UPSIDE_ON) {
            previewView.setUpsideVisibility(View.VISIBLE);
        } else {
            previewView.setUpsideVisibility(View.GONE);
        }
        if (baseProrertys.getSlowMotion().needDisplayByMode(appStateMode) && cameraProperties.getCurrentSlowMotion() == SlowMotion.SLOW_MOTION_ON) {
            previewView.setSlowMotionVisibility(View.VISIBLE);
        } else {
            previewView.setSlowMotionVisibility(View.GONE);
        }
        if (baseProrertys.getTimeLapseMode().needDisplayByMode(appStateMode)) {
            previewView.setTimeLapseModeVisibility(View.VISIBLE);
            try {
                previewView.setTimeLapseModeIcon(baseProrertys.getTimeLapseMode().getCurrentIcon());
            } catch (NullPointerException e) {
                e.printStackTrace();
            }
        } else {
            previewView.setTimeLapseModeVisibility(View.GONE);
        }
    }

    public void initPreview() {
        AppLog.i(TAG, "initPreview curMode=" + curAppStateMode);
        GlobalInfo.getInstance().setOnEventListener(new GlobalInfo.OnEventListener() {
            @Override
            public void eventListener(int sdkEventId) {
                switch (sdkEventId) {
                    case SDKEvent.EVENT_SDCARD_REMOVED:
                        MyToast.show(activity, R.string.dialog_card_removed);
                        if (baseProrertys.getImageSize().needDisplayByMode(curAppStateMode)) {
                            previewView.setRemainCaptureCount("0");
                        } else if (baseProrertys.getVideoSize().needDisplayByMode(curAppStateMode)) {
                            previewView.setRemainRecordingTimeText(ConvertTools.secondsToMinuteOrHours(0));
                        }
                        break;
                    case SDKEvent.EVENT_SDCARD_INSERT:
                        MyToast.show(activity, R.string.dialog_card_inserted);
                        if (baseProrertys.getImageSize().needDisplayByMode(curAppStateMode)) {
                            previewView.setRemainCaptureCount(String.valueOf(cameraProperties.getRemainImageNum()));
                        } else if (baseProrertys.getVideoSize().needDisplayByMode(curAppStateMode)) {
                            previewView.setRemainRecordingTimeText(ConvertTools.secondsToMinuteOrHours(cameraProperties.getRecordingRemainTime()));
                        }
                        break;
                }
            }
        });
        previewView.setMinZoomRate(1.0f);
        previewView.setMaxZoomRate(cameraProperties.getMaxZoomRatio() * 1.0f);
        previewView.updateZoomViewProgress(cameraProperties.getCurrentZoomRatio());
        int iCatchMode = cameraAction.getCurrentCameraMode();
        if (cameraState.isMovieRecording()) {
            AppLog.i(TAG, "camera is recording...");
            curAppStateMode = PreviewMode.APP_STATE_VIDEO_CAPTURE;
            curICatchMode = ICatchCamPreviewMode.ICH_CAM_VIDEO_PREVIEW_MODE;
            startVideoCaptureButtomChangeTimer();
            startRecordingLapseTimeTimer(cameraProperties.getVideoRecordingTime());
        } else if (cameraState.isTimeLapseVideoOn()) {
            AppLog.i(TAG, "camera is TimeLapseVideoOn...");
            curCamera.timeLapsePreviewMode = TimeLapseMode.TIME_LAPSE_MODE_VIDEO;
            curAppStateMode = PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE;
            curICatchMode = ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_VIDEO_PREVIEW_MODE;
            startVideoCaptureButtomChangeTimer();
            startRecordingLapseTimeTimer(cameraProperties.getVideoRecordingTime());
        } else if (cameraState.isTimeLapseStillOn()) {
            AppLog.i(TAG, "camera is TimeLapseStillOn...");
            curCamera.timeLapsePreviewMode = TimeLapseMode.TIME_LAPSE_MODE_STILL;
            curAppStateMode = PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE;
            curICatchMode = ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_STILL_PREVIEW_MODE;
            startVideoCaptureButtomChangeTimer();
            startRecordingLapseTimeTimer(cameraProperties.getVideoRecordingTime());
        } else if (curAppStateMode == PreviewMode.APP_STATE_NONE_MODE) {
            if (cameraProperties.cameraModeSupport(ICatchCamMode.ICH_CAM_MODE_CAMERA)) {
                curAppStateMode = PreviewMode.APP_STATE_STILL_PREVIEW;
                curICatchMode = ICatchCamPreviewMode.ICH_CAM_STILL_PREVIEW_MODE;
            } else if (cameraProperties.cameraModeSupport(ICatchCamMode.ICH_CAM_MODE_VIDEO)) {
                curAppStateMode = PreviewMode.APP_STATE_VIDEO_PREVIEW;
                curICatchMode = ICatchCamPreviewMode.ICH_CAM_VIDEO_PREVIEW_MODE;
            } else if (cameraProperties.cameraModeSupport(ICatchCamMode.ICH_CAM_MODE_TIMELAPSE)) {
                curAppStateMode = APP_STATE_TIMELAPSE_VIDEO_PREVIEW;
                curICatchMode = ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_VIDEO_PREVIEW_MODE;
            } else {
                curAppStateMode = PreviewMode.APP_STATE_VIDEO_PREVIEW;
                curICatchMode = ICatchCamPreviewMode.ICH_CAM_VIDEO_PREVIEW_MODE;
            }
        } else if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW) {
            AppLog.i(TAG, "initPreview curMode == PreviewMode.APP_STATE_VIDEO_PREVIEW");
            curICatchMode = ICatchCamPreviewMode.ICH_CAM_VIDEO_PREVIEW_MODE;
        } else if (curAppStateMode == APP_STATE_TIMELAPSE_VIDEO_PREVIEW) {
            AppLog.i(TAG, "initPreview curMode == PreviewMode.APP_STATE_TIMELAPSE_PREVIEW_VIDEO");
            curCamera.timeLapsePreviewMode = TimeLapseMode.TIME_LAPSE_MODE_VIDEO;
            curICatchMode = ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_VIDEO_PREVIEW_MODE;
        } else if (curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW) {
            AppLog.i(TAG, "initPreview curMode == PreviewMode.APP_STATE_TIMELAPSE_PREVIEW_STILL");
            curCamera.timeLapsePreviewMode = TimeLapseMode.TIME_LAPSE_MODE_STILL;
            curICatchMode = ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_STILL_PREVIEW_MODE;
        } else if (curAppStateMode == PreviewMode.APP_STATE_STILL_PREVIEW) {
            AppLog.i(TAG, "initPreview curMode == ICH_STILL_PREVIEW_MODE");
            changeCameraMode(curAppStateMode, ICatchCamPreviewMode.ICH_CAM_STILL_PREVIEW_MODE);
            curICatchMode = ICatchCamPreviewMode.ICH_CAM_STILL_PREVIEW_MODE;
        } else {
            curAppStateMode = PreviewMode.APP_STATE_VIDEO_PREVIEW;
            curICatchMode = ICatchCamPreviewMode.ICH_CAM_VIDEO_PREVIEW_MODE;
        }
        cameraAction.changePreviewMode(curICatchMode);
        createUIByMode(curAppStateMode);
        AppExecutors.runOnIoThread(this::refreshA6MaxRecordMode);

        // 检查并恢复A6Max录制状态（仅在A6Max相机且视频模式下）
        AppExecutors.runOnIoThread(this::checkAndRecoverA6MaxRecordState);
    }

    public void startVideoCaptureButtomChangeTimer() {
        AppLog.d(TAG, "startVideoCaptureButtomChangeTimer videoCaptureButtomChangeTimer=" + videoCaptureButtomChangeTimer);
        TimerTask task = new TimerTask() {
            @Override
            public void run() {
                if (videoCaptureButtomChangeFlag) {
                    videoCaptureButtomChangeFlag = false;
                    previewHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_CAPTURE || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE) {
                                previewView.setCaptureBtnBackgroundResource(R.drawable.video_recording_btn_on);
                            }
                        }
                    });
                } else {
                    videoCaptureButtomChangeFlag = true;
                    previewHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_CAPTURE || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE) {
                                previewView.setCaptureBtnBackgroundResource(R.drawable.video_recording_btn_off);
                            }
                        }
                    });
                }
            }
        };
        videoCaptureButtomChangeTimer = new Timer(true);
        videoCaptureButtomChangeTimer.schedule(task, 0, 1000);
    }

    public void stopVideoCaptureButtomChangeTimer() {
        AppLog.d(TAG, "stopVideoCaptureButtomChangeTimer videoCaptureButtomChangeTimer=" + videoCaptureButtomChangeTimer);
        if (videoCaptureButtomChangeTimer != null) {
            videoCaptureButtomChangeTimer.cancel();
        }
        previewView.setCaptureBtnBackgroundResource(R.drawable.video_recording_btn_on);
    }

    private void startRecordingLapseTimeTimer(int startTime) {
        if (!cameraProperties.hasFunction(PropertyId.VIDEO_RECORDING_TIME)) {
            return;
        }
        AppLog.i(TAG, "startRecordingLapseTimeTimer curMode=" + curAppStateMode);
        if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_CAPTURE || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE) {
            AppLog.i(TAG, "startRecordingLapseTimeTimer");
            if (recordingLapseTimeTimer != null) {
                recordingLapseTimeTimer.cancel();
            }
            lapseTime = startTime;
            recordingLapseTimeTimer = new Timer(true);
            previewView.setRecordingTimeVisibility(View.VISIBLE);
            TimerTask timerTask = new TimerTask() {
                @Override
                public void run() {
                    previewHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            previewView.setRecordingTime(ConvertTools.secondsToHours(lapseTime++));
                        }
                    });
                }
            };
            recordingLapseTimeTimer.schedule(timerTask, 0, 1000);
        }
    }

    private void stopRecordingLapseTimeTimer() {
        if (recordingLapseTimeTimer != null) {
            recordingLapseTimeTimer.cancel();
        }
        previewView.setRecordingTime("00:00:00");
        previewView.setRecordingTimeVisibility(View.GONE);
    }

    public void changePreviewMode(int previewMode) {
        AppLog.d(TAG, "changePreviewMode previewMode=" + previewMode);
        AppLog.d(TAG, "changePreviewMode curAppStateMode=" + curAppStateMode);
        long timeInterval = System.currentTimeMillis() - lastCilckTime;
        AppLog.d(TAG, "repeat click: timeInterval=" + timeInterval);
        if (System.currentTimeMillis() - lastCilckTime < 2000) {
            AppLog.d(TAG, "repeat click: timeInterval < 2000");
            return;
        } else {
            lastCilckTime = System.currentTimeMillis();
        }
        if (!checkModeSwitch(curAppStateMode)) {
            int resId = getSwitchErrorResId(curAppStateMode);
            if (resId > 0) {
                MyToast.show(activity, resId);
            }
            return;
        }
        modeSwitchBeep.start();
        if (previewMode == PreviewMode.APP_STATE_VIDEO_MODE) {
            if (curAppStateMode == PreviewMode.APP_STATE_STILL_PREVIEW || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW || curAppStateMode == APP_STATE_TIMELAPSE_VIDEO_PREVIEW) {
                stopPreview();
                changeCameraMode(PreviewMode.APP_STATE_VIDEO_PREVIEW, ICatchCamPreviewMode.ICH_CAM_VIDEO_PREVIEW_MODE);
            }
        } else if (previewMode == PreviewMode.APP_STATE_STILL_MODE) {
            if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW || curAppStateMode == APP_STATE_TIMELAPSE_VIDEO_PREVIEW) {
                stopPreview();
                changeCameraMode(PreviewMode.APP_STATE_STILL_PREVIEW, ICatchCamPreviewMode.ICH_CAM_STILL_PREVIEW_MODE);
            }
        } else if (previewMode == PreviewMode.APP_STATE_TIMELAPSE_MODE) {
            if (curAppStateMode == PreviewMode.APP_STATE_STILL_PREVIEW || curAppStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW) {
                stopPreview();
                if (curCamera.timeLapsePreviewMode == TimeLapseMode.TIME_LAPSE_MODE_VIDEO) {
                    changeCameraMode(PreviewMode.APP_STATE_TIMELAPSE_VIDEO_PREVIEW, ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_VIDEO_PREVIEW_MODE);
                } else {
                    changeCameraMode(PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW, ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_STILL_PREVIEW_MODE);
                }
            }
        }
    }

    private long photoCaptureStartTime, photoCaptureEndTime;
    private static boolean isWaitA6Rotate = true;
    private int countTimes = 0;

    private void startPhotoCapture() {
        LogUtils.file("拍摄：开始拍摄");
        previewView.setCaptureBtnEnAbility(false);
        previewView.setCaptureBtnBackgroundResource(R.drawable.still_capture_btn_off);
        PhotoCapture photoCapture = new PhotoCapture();
        if (isA6Camera) {
            MyProgressDialog.showProgressDialog(activity, PanoramaApp.getContext().getString(R.string.processing));
            countTimes = 0;
            isWaitA6Rotate = true;
            if (!CameraUtils.isA8()) {
                photoCapture.addOnStopPreviewListener(this::stopPreview);
            }

            startA6CaptureTimer();//开启A6 计时器

            new Thread(() -> {//开个子线程，不断查询相机状态，如果相机是待机状态，就开始拍摄
                cameraProperties.setPropertyValue(PropertyId.A6_ROTATE_MOTOR_STATE, A6RotateMotorState.ROTATE_START);
                int motorState;
                LogUtil.d("循环询问相机是否可以拍照开始");
                LogUtils.file("拍摄：循环询问相机是否可以拍照开始");
                for (; ; ) {
                    if (activity == null || activity.isFinishing()) {
                        LogUtils.e("页面已退出，停止拍摄");
                        LogUtils.file("拍摄过程中切到了后台，退出拍摄页面");
                        ToastUtil.showLongToast("已停止拍摄。拍摄时，请不要退出拍摄页面");
                        cancelA6CaptureTimer();//取消定时器
                        break;
                    }
                    try {
                        Thread.sleep(200);//卡一下，不要太频繁
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    motorState = getCurrentPropertyValue(PropertyId.A6_ROTATE_MOTOR_STATE);
                    if (motorState == 3) {
                        LogUtils.file("拍摄：相机出现异常，正在复位（3）");
                        cameraProperties.setPropertyValue(PropertyId.A6_ROTATE_MOTOR_STATE, A6RotateMotorState.ROTATE_REBOOT);
                        ToastUtils.showLong("相机出现异常，正在复位");
                        cancelA6CaptureTimer();//取消定时器
                        break;
                    }
//                    LogUtil.d("循环等待相机可以拍照，motorState="+motorState);
                    if (motorState == A6RotateMotorState.ROTATE_END || motorState == A6RotateMotorState.ROTATE_OFF) {//待机状态（当前不在旋转状态）
                        LogUtil.d("循环询问相机是否可以拍照结束：相机准备好了：motorState=" + motorState);
                        LogUtils.file("拍摄：循环询问相机是否可以拍照结束,相机准备好了：motorState=" + motorState);
                        photoCaptureStartTime = System.currentTimeMillis();
                        photoCapture.startCapture();//开始通知相机拍照

                        //开始检查中断指令
                        curA6CaptureNeedCheckInterrupt.set(true);
                        break;
                    }
                }
            }).start();
            return;
        }
        if (cameraProperties.hasFunction(PropertyId.CAPTURE_DELAY_MODE)) {
            photoCapture.addOnStopPreviewListener(() -> {
                if (!cameraProperties.hasFunction(0xd704)) {
                    stopPreview();
                }
            });
            photoCapture.setOnCaptureCompletedListener(() -> previewHandler.post(() -> previewView.setCaptureBtnEnAbility(true)));
            photoCapture.startCapture();
        } else {
            stillCaptureStartBeep.start();
            if (!cameraProperties.hasFunction(0xd704)) {
                stopPreview();
            }
            MyProgressDialog.showProgressDialog(activity, R.string.dialog_capturing);
            new Thread(() -> {
                final boolean ret = cameraAction.capturePhoto();
                previewHandler.post(() -> {
                    if (!ret) {
                        MyToast.show(activity, R.string.text_operation_failed);
                        curAppStateMode = PreviewMode.APP_STATE_STILL_PREVIEW;
                    }
                    previewView.setCaptureBtnEnAbility(true);
                    MyProgressDialog.closeProgressDialog();
                });
            }).start();
        }
    }

    public synchronized boolean disconnectCamera() {
        if (curCamera != null) {
            GlobalInfo.getInstance().delEventListener(ICatchCamEventID.ICH_CAM_EVENT_SDCARD_REMOVED);
            GlobalInfo.getInstance().delEventListener(ICatchCamEventID.ICH_CAM_EVENT_SDCARD_IN);
            GlobalInfo.getInstance().delEventListener(ICatchCamEventID.ICH_CAM_EVENT_CONNECTION_DISCONNECTED);
            GlobalInfo.getInstance().delete();
            return curCamera.disconnect();
        } else {
            return false;
        }
    }

    public void delConnectFailureListener() {
    }

    public void unregisterWifiSSReceiver() {
        if (wifiSSReceiver != null) {
            activity.unregisterReceiver(wifiSSReceiver);
            wifiSSReceiver = null;
        }
    }

    public void zoomIn() {
        if (curAppStateMode == PreviewMode.APP_STATE_STILL_CAPTURE || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE) {
            return;
        }
        zoomInOut.zoomIn();
        previewView.updateZoomViewProgress(cameraProperties.getCurrentZoomRatio());
    }

    public void zoomOut() {
        if (curAppStateMode == PreviewMode.APP_STATE_STILL_CAPTURE || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE) {
            return;
        }
        zoomInOut.zoomOut();
        previewView.updateZoomViewProgress(cameraProperties.getCurrentZoomRatio());
    }

    public void zoomBySeekBar() {
        zoomInOut.addZoomCompletedListener(new ZoomInOut.ZoomCompletedListener() {
            @Override
            public void onCompleted(final float currentZoomRate) {
                previewHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        MyProgressDialog.closeProgressDialog();
                        AppLog.i(TAG, "addZoomCompletedListener currentZoomRate =" + currentZoomRate);
                        previewView.updateZoomViewProgress(currentZoomRate);
                    }
                });
            }
        });
        zoomInOut.startZoomInOutThread(this);
        MyProgressDialog.showProgressDialog(activity, null);
    }

    public void showZoomView() {
        if (curAppStateMode == PreviewMode.APP_STATE_STILL_CAPTURE || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE || (cameraProperties.hasFunction(ICatchCamProperty.ICH_CAM_CAP_DATE_STAMP) == true && ICatchCamDateStamp.ICH_CAM_DATE_STAMP_OFF != cameraProperties.getCurrentDateStamp())) {
            return;
        }
        previewView.showZoomView();
    }

    public float getMaxZoomRate() {
        return previewView.getZoomViewMaxZoomRate();
    }

    public float getZoomViewProgress() {
        AppLog.d(TAG, "getZoomViewProgress value=" + previewView.getZoomViewProgress());
        return previewView.getZoomViewProgress();
    }

    public void showSettingDialog(int position) {
        OptionSetting optionSetting = new OptionSetting(this);
        if (settingMenuList != null && settingMenuList.size() > 0) {
            optionSetting.addSettingCompleteListener(new OnSettingCompleteListener() {
                @Override
                public void onOptionSettingComplete() {
                    AppLog.d(TAG, "onOptionSettingComplete");
                    settingMenuList = UIDisplaySource.getInstance().getList(currentSettingMenuMode, curCamera);
                    settingListAdapter.notifyDataSetChanged();
                }

                @Override
                public void settingVideoSizeComplete() {
                    AppLog.d(TAG, "settingVideoSizeComplete curAppStateMode=" + curAppStateMode);
                }

                @Override
                public void onResetDoCaptureBtn(int timeLapseMode) {
                    if (timeLapseMode == TimeLapseMode.TIME_LAPSE_MODE_STILL) {
                        previewView.setCaptureBtnBackgroundResource(R.drawable.still_capture_btn);
                    } else if (timeLapseMode == TimeLapseMode.TIME_LAPSE_MODE_VIDEO) {
                        previewView.setCaptureBtnBackgroundResource(R.drawable.video_recording_btn_on);
                    }
                }

                @Override
                public void settingTimeLapseModeComplete(int timeLapseMode) {
                    if (timeLapseMode == TimeLapseMode.TIME_LAPSE_MODE_STILL) {
                        if (cameraAction.changePreviewMode(ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_STILL_PREVIEW_MODE)) {
                            curICatchMode = ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_STILL_PREVIEW_MODE;
                            curAppStateMode = PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW;
                            baseProrertys.getTimeLapseStillInterval().initTimeLapseInterval();
                        }
                    } else if (timeLapseMode == TimeLapseMode.TIME_LAPSE_MODE_VIDEO) {
                        if (cameraAction.changePreviewMode(ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_VIDEO_PREVIEW_MODE)) {
                            curICatchMode = ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_VIDEO_PREVIEW_MODE;
                            curAppStateMode = PreviewMode.APP_STATE_TIMELAPSE_VIDEO_PREVIEW;
                            baseProrertys.getTimeLapseVideoInterval().initTimeLapseInterval();
                        }
                    }
                }
            });
            optionSetting.showSettingDialog(settingMenuList.get(position).name, activity);
        }
    }

    public void showPvModePopupWindow() {
        AppLog.d(TAG, "showPvModePopupWindow curAppStateMode=" + curAppStateMode);
        if (!checkModeSwitch(curAppStateMode)) {
            int resId = getSwitchErrorResId(curAppStateMode);
            if (resId > 0) {
                MyToast.show(activity, resId);
            }
            return;
        }
        previewView.showPopupWindow(curAppStateMode);
        previewView.setCaptureRadioBtnVisibility(cameraProperties.cameraModeSupport(ICatchCamMode.ICH_CAM_MODE_CAMERA) ? View.VISIBLE : View.GONE);
        previewView.setVideoRadioBtnVisibility(cameraProperties.cameraModeSupport(ICatchCamMode.ICH_CAM_MODE_VIDEO) ? View.VISIBLE : View.GONE);
        if (isA6MaxCamera) {
            previewView.setTimeLapseRadioBtnVisibility(View.GONE);
        } else {
            previewView.setTimeLapseRadioBtnVisibility(cameraProperties.cameraModeSupport(ICatchCamMode.ICH_CAM_MODE_TIMELAPSE) ? View.VISIBLE : View.GONE);
        }
        if (curAppStateMode == PreviewMode.APP_STATE_STILL_PREVIEW) {
            previewView.setCaptureRadioBtnChecked(true);
        } else if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW) {
            previewView.setVideoRadioBtnChecked(true);
        } else if (curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_PREVIEW) {
            previewView.setTimeLapseRadioChecked(true);
        }
    }

    public void onDestroy() {
        if (a6CaptureTask != null && !a6CaptureTask.isCancelled()) {
            a6CaptureTask.cancel(true);
        }
        a6CaptureScheduler.shutdownNow();

        // 清理A6Max录像相关定时器
        if (isA6MaxCamera) {
            LogUtils.d(TAG, "页面销毁，清理A6Max录像相关定时器");
            LogUtils.file("A6Max录像：页面销毁，清理相关定时器");
            stopA6MaxRecordMonitor();
            isA6MaxRecordProcessing = false;
        }
    }

    //    private ArrayList<String> cacheFiles = null;
//    private ArrayList<String> downFiles = null;


    //相机回调数据
    private class PreviewHandler extends Handler {
        //上一次收到拍照完成事件，去重用
        private long lastRecvCaptureCompletedEvent = 0;

        @Override
        public void handleMessage(Message msg) {
            Tristate ret = Tristate.FALSE;
            LogUtils.file("收到相机发过来的事件：" + msg.what);
            switch (msg.what) {
                case SDKEvent.EVENT_BATTERY_ELETRIC_CHANGED:
                    if (msg.arg1 < 100) {
                        refreshBatteryLevel();//直接拿电量刷新
                    }
//                    //0:没电；1.1/3电量；2.2/3 电量 3.满电
//                    int batteryLevel = msg.arg1;
//                    int resId = ThumbnailOperation.getBatteryLevelIcon(batteryLevel);
//                    if (resId > 0) {
//                        previewView.setBatteryIcon(resId);
//                        if (batteryLevel < 20) {
//                        }
//                    }
                    LogUtils.file("收到电池电量变化通知：" + msg.arg1);
                    break;
                case SDKEvent.EVENT_CONNECTION_FAILURE:
                    try {
                        stopPreview();
                        delEvent();
                        disconnectCamera();
                        finishActivity();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    break;
                case SDKEvent.EVENT_SD_CARD_FULL:
                    AppDialog.showDialogWarn(activity, R.string.dialog_card_full);
                    break;
                case SDKEvent.EVENT_VIDEO_OFF://only receive if fw request to stopMPreview video recording
                    if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_CAPTURE || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE) {
                        // 统一处理EVENT_VIDEO_OFF，包括A6Max
                        if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_CAPTURE) {
                            curAppStateMode = PreviewMode.APP_STATE_VIDEO_PREVIEW;
                        } else {
                            curAppStateMode = PreviewMode.APP_STATE_TIMELAPSE_VIDEO_PREVIEW;
                        }
                        stopVideoCaptureButtomChangeTimer();
                        stopRecordingLapseTimeTimer();
                        previewView.setRemainRecordingTimeText(ConvertTools.secondsToMinuteOrHours(cameraProperties.getRecordingRemainTime()));

                        // A6Max特殊处理：停止录制监控和重置状态
                        if (isA6MaxCamera && isA6MaxRecordProcessing) {
                            LogUtils.d(TAG, "A6Max录像：收到EVENT_VIDEO_OFF，停止录制");
                            LogUtils.file("A6Max录像：收到EVENT_VIDEO_OFF，停止录制");
                            isA6MaxRecordProcessing = false;
                            stopA6MaxRecordMonitor();
                            // 执行A6Max录像后处理
                            handleA6MaxRecordPostProcess();
                        }
                    }
                    break;
                case SDKEvent.EVENT_VIDEO_ON:
                    // 通用的EVENT_VIDEO_ON处理逻辑
                    if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW) {
                        curAppStateMode = PreviewMode.APP_STATE_VIDEO_CAPTURE;
                        startVideoCaptureButtomChangeTimer();
                        startRecordingLapseTimeTimer(0);

                        // A6Max特殊处理：启动录制监控
                        if (isA6MaxCamera) {
                            LogUtils.d(TAG, "A6Max录像：收到EVENT_VIDEO_ON，开始录制");
                            LogUtils.file("A6Max录像：收到EVENT_VIDEO_ON，开始录制");
                            isA6MaxRecordProcessing = true;
                            startA6MaxRecordMonitor();
                        }
                    } else if (curAppStateMode == APP_STATE_TIMELAPSE_VIDEO_PREVIEW) {
                        curAppStateMode = PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE;
                        startVideoCaptureButtomChangeTimer();
                        startRecordingLapseTimeTimer(0);
                    }
                    break;
                case SDKEvent.EVENT_CAPTURE_START:
                    if (curAppStateMode != PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE) {
                        return;
                    }
                    continuousCaptureBeep.start();
                    MyToast.show(activity, R.string.capture_start);
                    break;
                case SDKEvent.EVENT_ROTATE_CAPTURE_COMPLETED:
                    LogUtils.file("拍摄：A6旋转拍照完成（EVENT_ROTATE_CAPTURE_COMPLETED）");
                    MyToast.show(activity, "A6旋转拍照完成！！！！");
                    LogUtil.d("A6旋转拍照完成！！！！:EVENT_ROTATE_CAPTURE_COMPLETED");
                    break;
                case SDKEvent.EVENT_CAPTURE_COMPLETED:
                    LogUtil.i("单次拍照完成:EVENT_CAPTURE_COMPLETED：curAppStateMode=" + curAppStateMode + ":countTimes=" + countTimes);
                    LogUtils.file("拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=" + countTimes);
                    //这里发现可能会在2毫秒内重复收到事件
                    if (System.currentTimeMillis() - lastRecvCaptureCompletedEvent < 100) {
                        LogUtil.d("太快了，跳过");
                        LogUtils.file("拍摄：100毫秒内的事件忽略（EVENT_CAPTURE_COMPLETED）");
                        return;
                    }
                    lastRecvCaptureCompletedEvent = System.currentTimeMillis();
                    if (isA6Camera && curAppStateMode == PreviewMode.APP_STATE_STILL_CAPTURE) {
                        countTimes++;
                        int curShotTimes = getCurrentPropertyValue(PropertyId.A6_ROTATE_SHOT_TIMES);
                        LogUtil.d("curShotTimes=" + curShotTimes);
                        LogUtils.file("拍摄：获取当前拍摄进度（0xD761）：" + curShotTimes);
                        photoCaptureEndTime = System.currentTimeMillis();
                        if (countTimes == 1) {
                            AppToast.show(activity, PanoramaApp.getContext().getString(R.string.take_photo1) + (photoCaptureEndTime - photoCaptureStartTime) + "ms", Toast.LENGTH_LONG);
                        } else if (countTimes == 2) {
                            AppToast.show(activity, PanoramaApp.getContext().getString(R.string.take_photo2) + (photoCaptureEndTime - photoCaptureStartTime) + "ms", Toast.LENGTH_LONG);
                        } else if (countTimes == 3) {
                            AppToast.show(activity, PanoramaApp.getContext().getString(R.string.take_photo3) + (photoCaptureEndTime - photoCaptureStartTime) + "ms", Toast.LENGTH_LONG);
                        } else if (countTimes == 4) {
                            AppToast.show(activity, PanoramaApp.getContext().getString(R.string.take_photo4) + (photoCaptureEndTime - photoCaptureStartTime) + "ms", Toast.LENGTH_LONG);
                            LogUtils.file("拍摄：理论上上拍摄完毕（countTimes == 4）");
                            new Thread(() -> {
                                LogUtil.d("循环询问相机是否已复位开始");
                                LogUtils.file("拍摄：循环询问相机是否已复位开始");
                                Long startAskResetStartTime = System.currentTimeMillis();//询问开始时间
                                for (; ; ) {
                                    int motorState = getCurrentPropertyValue(PropertyId.A6_ROTATE_MOTOR_STATE);
                                    int shotTimes = getCurrentPropertyValue(PropertyId.A6_ROTATE_SHOT_TIMES);
                                    if (motorState == 3) {
                                        cancelA6CaptureTimer();//停止计时器

                                        cameraProperties.setPropertyValue(PropertyId.A6_ROTATE_MOTOR_STATE, A6RotateMotorState.ROTATE_REBOOT);
                                        ToastUtils.showLong("相机出现异常，正在复位");
                                        LogUtils.file("拍摄：循环询问相机是否已复位结束：复位异常：motorState=" + motorState);
                                        break;
                                    }
//                                    LogUtil.d("循环判断相机是否已复位，此时相机的旋转状态是："+motorState+"；拍照times 是："+shotTimes);
                                    if (A6RotateMotorState.ROTATE_OFF == motorState && A6RotateShotTimes.SHOT_4TH == shotTimes) {
                                        cancelA6CaptureTimer();//停止计时器

                                        LogUtil.i("相机复位结束，重置拍摄状态：ROTATE_OFF");
                                        LogUtils.file("拍摄：循环询问相机是否已复位结束：相机已复位");
                                        previewView.setCaptureBtnEnAbility(true);
                                        previewView.setCaptureBtnBackgroundResource(R.drawable.still_capture_btn);
                                        MyProgressDialog.closeProgressDialog();
                                        AppToast.show(activity, PanoramaApp.getContext().getString(R.string.take_photo_finish));
                                        isWaitA6Rotate = false;

                                        //fixme 自动拍摄，测试测试用
                                        if (openAutoCaptureWithA6 && BuildConfig.DEBUG) {
                                            previewHandler.postDelayed(PreviewPresenter.this::startOrStopCapture, 3000);
                                        }
                                        //fixme 自动拍摄，测试测试用
                                        break;
                                    }
                                    if (startAskResetStartTime != null && (System.currentTimeMillis() - startAskResetStartTime) > (60000)) {//询问大于一分钟，视为卡住了
                                        LogUtils.file("！！！警告：拍摄：询问相机是否复位结束超过一分钟，可能已经卡住了。" +
                                                "motorState=" + motorState + ",shotTimes=" + shotTimes + "：警告！！！");
                                        startAskResetStartTime = null;//警告一次就好了
                                    }
                                    try {
                                        Thread.sleep(200);//别太频繁了
                                    } catch (Exception e) {
                                        LogUtil.e(e.toString());
                                        break;
                                    }
                                }
                            }).start();
                        }
                        curAppStateMode = PreviewMode.APP_STATE_STILL_PREVIEW;
                        new Thread(() -> {
                            startPreview();
                            final String remainImageNum = String.valueOf(cameraProperties.getRemainImageNum());
                            previewHandler.post(() -> previewView.setRemainCaptureCount(remainImageNum));
                        }).start();
                        int motorState = getCurrentPropertyValue(PropertyId.A6_ROTATE_MOTOR_STATE);
                        int shotTimes = getCurrentPropertyValue(PropertyId.A6_ROTATE_SHOT_TIMES);
                        //这会相机没有那么快OFF，所以这个条件应该有问题，不应该去判断OFF
//                        if (A6RotateMotorState.ROTATE_END == motorState && A6RotateShotTimes.SHOT_4TH == shotTimes) {
                        if (countTimes == 4) {
                            LogUtil.d("第四次图片，不需要询问相机是否可以拍照");
                        } else {//4次拍照未完成
                            LogUtil.d("循环询问相机是否可以开始下一次拍照开始，countTimes=" + countTimes);
                            new Thread(() -> {
                                int motorState1;
                                int shotTimes1;
                                LogUtils.file("拍摄：循环询问相机是否可以进行下一次拍照开始");
                                Long startAskResetStartTime = System.currentTimeMillis();//询问开始时间
                                for (; ; ) {//这个是等待相机可以拍照的时候，启动拍照，拍完照这个进程就结束了
                                    shotTimes1 = getCurrentPropertyValue(PropertyId.A6_ROTATE_SHOT_TIMES);
                                    motorState1 = getCurrentPropertyValue(PropertyId.A6_ROTATE_MOTOR_STATE);
                                    if (motorState1 == 0) {
                                        //可能有异常
                                    }
                                    if (motorState1 == 3) {
                                        cancelA6CaptureTimer();//停止计时器
                                        cameraProperties.setPropertyValue(PropertyId.A6_ROTATE_MOTOR_STATE, A6RotateMotorState.ROTATE_REBOOT);
                                        ToastUtils.showLong("相机出现异常，正在复位");
                                        break;
                                    }
                                    if (A6RotateMotorState.ROTATE_END == motorState1) {//当前是单次拍照结束状态（相机闲置）
                                        LogUtils.file("拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：" +
                                                "motorState1=" + motorState1 + ",shotTimes1=" + shotTimes1);
                                        LogUtil.d("循环询问相机是否可以开始下一次拍照结束，开启拍照：motorState1=" + motorState1);
                                        previewView.hideZoomView();
                                        curAppStateMode = PreviewMode.APP_STATE_STILL_CAPTURE;
                                        PhotoCapture photoCapture = new PhotoCapture();
                                        if (!CameraUtils.isA8()) {
                                            photoCapture.addOnStopPreviewListener(PreviewPresenter.this::stopPreview);
                                        }
                                        photoCaptureStartTime = System.currentTimeMillis();
                                        photoCapture.startCapture();//启动拍照
                                        break;//退出循环
                                    }
                                    if (!isWaitA6Rotate) {//已经不是a6 旋转状态，就退出循环，不然就判断一下
                                        break;
                                    }
                                    if (startAskResetStartTime != null && (System.currentTimeMillis() - startAskResetStartTime) > (60000)) {//询问大于一分钟，视为卡住了
                                        LogUtils.file("！！！警告：拍摄：询问相机是否复位结束超过一分钟，可能已经卡住了。" +
                                                "motorState=" + motorState + ",shotTimes=" + shotTimes + "：警告！！！");
                                        startAskResetStartTime = null;//警告一次就好了
                                    }
                                    try {
                                        Thread.sleep(100);
                                    } catch (Exception e) {
                                        LogUtil.e(e.toString());
                                        break;
                                    }
//
                                    //若上一次拍摄失败，退出应用了，这里可以兜底
                                    if (A6RotateMotorState.ROTATE_OFF == motorState1 && A6RotateShotTimes.SHOT_4TH == shotTimes1) {
                                        cancelA6CaptureTimer();//停止计时器

                                        LogUtils.file("触发了拍摄异常终止：A6RotateMotorState.ROTATE_OFF == motorState1 && A6RotateShotTimes.SHOT_4TH == shotTimes1");
                                        LogUtil.w("触发了拍摄异常终止：A6RotateMotorState.ROTATE_OFF == motorState1 && A6RotateShotTimes.SHOT_4TH == shotTimes1");
                                        previewView.setCaptureBtnEnAbility(true);
                                        previewView.setCaptureBtnBackgroundResource(R.drawable.still_capture_btn);
                                        MyProgressDialog.closeProgressDialog();
                                        AppToast.show(activity, PanoramaApp.getContext().getString(R.string.take_photo_finish));
                                        isWaitA6Rotate = false;
                                    }
                                }
                            }).start();
                        }
                        return;
                    } else if (curAppStateMode == PreviewMode.APP_STATE_STILL_CAPTURE) {
                        curAppStateMode = PreviewMode.APP_STATE_STILL_PREVIEW;
                        MyProgressDialog.showProgressDialog(activity, R.string.action_processing);
                        new Thread(() -> {
                            if (!cameraProperties.hasFunction(0xd704)) {
                                previewHandler.postDelayed(PreviewPresenter.this::startPreview, 11); // 延时1秒,防止视频流冲突
                            }
                            final String remainImageNum = String.valueOf(cameraProperties.getRemainImageNum());
                            previewHandler.post(() -> {
                                previewView.setCaptureBtnEnAbility(true);
                                previewView.setCaptureBtnBackgroundResource(R.drawable.still_capture_btn);
                                previewView.setRemainCaptureCount(remainImageNum);
                                MyProgressDialog.closeProgressDialog();
                            });
                        }).start();
                        return;
                    }
                    if (curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE) {
                        previewView.setCaptureBtnEnAbility(true);
                        previewView.setCaptureBtnBackgroundResource(R.drawable.still_capture_btn);
                        previewView.setRemainCaptureCount(String.valueOf(cameraProperties.getRemainImageNum()));
                        MyToast.show(activity, R.string.capture_completed);
                    }
                    break;
                case SDKEvent.EVENT_FILE_ADDED:
                    if (!SPUtils.getInstance().getBoolean("isAutoDown", true)) return;
                    if (!isA6Camera) return;
                    ICatchFile filec = (ICatchFile) msg.obj;
                    if (filec != null) {
                        String fileName = filec.getFileName();
                        //文件名格式：20240608_204651_2C.JPG
                        LogUtil.d("收到添加文件的消息：" + fileName);
                        if (TextUtils.isEmpty(fileName) || fileName.equals("JPG") || fileName.toLowerCase().endsWith("mp4")) {
                            return;
                        }//todo 这里考虑下可以加异常处理，如果上一次拍一组照片3 张，app 异常中断了，下一次 进来拍照的话会继续上一次的拍照过程
                        String groupName = getGroupName(fileName);

                        if (!reciverFileMap.containsKey(groupName)) {
                            LogUtil.w("收到一组新的图片，重新计数");
//                            countTimes = 0;//重新计数，第一张已拍完
                        }

                        ///开个线程下载文件
                        handleAddFileWithMultiThreaded(filec);
                    }


                    break;
                case SDKEvent.EVENT_TIME_LAPSE_STOP:
                    if (curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE) {
                        stopVideoCaptureButtomChangeTimer();
                        stopRecordingLapseTimeTimer();
                        previewView.setRemainCaptureCount(new Integer(cameraProperties.getRemainImageNum()).toString());
                        curAppStateMode = APP_STATE_TIMELAPSE_VIDEO_PREVIEW;
                    } else if (curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE) {
                        stopRecordingLapseTimeTimer();
                        previewView.setRemainCaptureCount(new Integer(cameraProperties.getRemainImageNum()).toString());
                        curAppStateMode = PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW;
                    }
                    break;
                case SDKEvent.EVENT_VIDEO_RECORDING_TIME:
                    startRecordingLapseTimeTimer(0);
                    break;
                case SDKEvent.EVENT_FILE_DOWNLOAD:
                    if (!AppInfo.autoDownloadAllow) {
                        AppLog.d(TAG, "GlobalInfo.autoDownload == false");
                        return;
                    }
                    final String path = StorageUtil.getRootPath(activity) + AppInfo.AUTO_DOWNLOAD_PATH;
                    File directory = new File(path);
                    if (FileTools.getFileSize(directory) / 1024 >= AppInfo.autoDownloadSizeLimit * 1024 * 1024) {
                        AppLog.d(TAG, "can not download because size limit");
                        return;
                    }
                    final ICatchFile file = (ICatchFile) msg.obj;
                    FileOper.createDirectory(path);
                    new Thread(() -> {
                        AppLog.d(TAG, "receive downloadFile file =" + file);
                        AppLog.d(TAG, "receive downloadFile path =" + path);
                        boolean retValue = fileOperation.downloadFile(file, path + file.getFileName());
                        if (retValue) {
                            previewHandler.post(() -> {
                                String path1 = path + file.getFileName();
                                Bitmap bitmap = BitmapTools.getImageByPath(path1, 150, 150);
                                previewView.setAutoDownloadBitmap(bitmap);
                            });
                        }
                        AppLog.d(TAG, "receive downloadFile retvalue =" + retValue);
                    }).start();
                    break;
                case AppMessage.SETTING_OPTION_AUTO_DOWNLOAD:
                    Boolean switcher = (Boolean) msg.obj;
                    if (switcher) {
                        AppInfo.autoDownloadAllow = true;
                        previewView.setAutoDownloadVisibility(View.VISIBLE);
                    } else {
                        AppInfo.autoDownloadAllow = false;
                        previewView.setAutoDownloadVisibility(View.GONE);
                    }
                    break;
                case SDKEvent.EVENT_SDCARD_INSERT:
                    AppDialog.showDialogWarn(activity, R.string.dialog_card_inserted);
                    break;
                default:
                    super.handleMessage(msg);
                    break;
            }
        }
    }

    //上一次A6 拍摄时的进度计数器，线程安全
    private final AtomicLong curA6CaptureShotTime = new AtomicLong();
    private final AtomicBoolean curA6CaptureNeedCheckInterrupt = new AtomicBoolean();

    ///对于A6 开一个计时器，如果拍摄超时了，就终止整个拍摄过程
    private void startA6CaptureTimer() {
        //先关掉之前的计时器
        cancelA6CaptureTimer();

        Runnable task = () -> {
            if (Thread.currentThread().isInterrupted()) {
                LogUtil.d("拍摄：定时器被主动中断了");
                LogUtils.file("拍摄：定时器被主动中断了");
                return;
            }
            int motorState = getCurrentPropertyValue(PropertyId.A6_ROTATE_MOTOR_STATE, false);
            LogUtil.d("拍摄：motorState = " + motorState);
            LogUtils.file("拍摄：motorState = " + motorState);
            //收到相机发送的终止指令或超时
            if ((System.currentTimeMillis() - curA6CaptureShotTime.get()) > 100 * 1000
                    || (motorState == A6RotateMotorState.ROTATE_INTERRUPT && curA6CaptureNeedCheckInterrupt.get())) {//100秒没拍完直接结束
                LogUtil.d("拍摄：拍摄超时，计时器中断拍摄过程，motorState = " + motorState);
                LogUtils.file("拍摄：拍摄超时或异常中断，计时器中断拍摄过程，退出拍摄页面，motorState = " + motorState);
                //取消定时器
                cancelA6CaptureTimer();
                if (motorState == A6RotateMotorState.ROTATE_INTERRUPT) {
                    LogUtil.e("拍摄：收到中断指令，直接中断拍摄过程");
                    LogUtils.file("拍摄：收到中断指令，直接中断拍摄过程");
                } else {
                    //发送中断指令
                    cameraProperties.setPropertyValue(PropertyId.A6_ROTATE_MOTOR_STATE, A6RotateMotorState.ROTATE_INTERRUPT);
                    LogUtil.e("拍摄：计时器计时结束，向相机发送中断指令");
                    LogUtils.file("拍摄：计时器计时结束，向相机发送中断指令");
                }

//                previewView.setCaptureBtnEnAbility(true);
//                previewView.setCaptureBtnBackgroundResource(R.drawable.still_capture_btn);
                MyProgressDialog.closeProgressDialog();
                AppToast.show(activity, "拍摄异常");
                activity.finish();//直接干掉activity
//
//                curAppStateMode = PreviewMode.APP_STATE_STILL_PREVIEW;
//                startPreview();
//                final String remainImageNum = String.valueOf(cameraProperties.getRemainImageNum());
//                previewHandler.post(() -> previewView.setRemainCaptureCount(remainImageNum));
                return;
            }
        };

        //重置计数器
        curA6CaptureShotTime.set(System.currentTimeMillis());
        //这里不用检查中断指令，等到真正开始的时候才检查
        curA6CaptureNeedCheckInterrupt.set(false);

        // 启动定时任务，每 3秒执行一次,用来判断是否需要断开
        try {
            a6CaptureTask = a6CaptureScheduler.scheduleWithFixedDelay(task, 0, 3, TimeUnit.SECONDS);
            LogUtils.file("拍摄：A6 开启计时器");
        } catch (Exception e) {
            LogUtil.e("拍摄：启动计时器异常：" + e.getMessage());
            LogUtils.file("拍摄：启动计时器异常：" + e.getMessage());
        }
    }

    //中断计时器
    private void cancelA6CaptureTimer() {
        LogUtil.d("拍摄：中断计时器");
        LogUtils.file("拍摄：中断计时器");
        if (a6CaptureTask != null && !a6CaptureTask.isCancelled()) {
            a6CaptureTask.cancel(true);
        }
    }

    ///检查是否在下载文件，如果是的话，卡住界面
    private void checkIsDownloadingFile() {
        if (!isDownloadingFile) {
            return;
        }
        //最多两分钟，防止中途出现异常卡住整个App
        checkDownloadFinish(2 * 60);
    }

    private void checkDownloadFinish(int deathLineSec) {
        String dialogStr = "自动下载文件中，请勿关闭页面";
        MyProgressDialog.showProgressDialog(activity, dialogStr);
        // 使用 lambda 创建循环任务
        Runnable checkTask = new Runnable() {
            private int remainingTime = deathLineSec;

            @Override
            public void run() {
                if (!isDownloadingFile) { // 下载完成
                    MyProgressDialog.closeProgressDialog();
                    ToastUtil.showShortToast("文件自动下载成功，正在后台进行拼接");
                } else {
                    if (remainingTime > 0) {
                        remainingTime--;
                        previewHandler.postDelayed(this, 1000);
                        if (!MyProgressDialog.isShowing()) {
                            MyProgressDialog.showProgressDialog(activity, dialogStr);
                        }
                    } else { // 超时处理
                        MyProgressDialog.closeProgressDialog();
                        MyToast.show(activity, "下载文件超时，请手动下载");
                    }
                }
            }
        };

        // 初始执行任务
        previewHandler.postDelayed(checkTask, 1000);
    }

    //处理收到新文件
    private void handleAddFileWithMultiThreaded(@NonNull ICatchFile filec) {
        String groupName = getGroupName(filec.getFileName());
        List<ICatchFile> groupReceiveFileList;
        if (reciverFileMap.containsKey(groupName)) {
            groupReceiveFileList = reciverFileMap.get(groupName);
        } else {
            groupReceiveFileList = new ArrayList<>();
            reciverFileMap.put(groupName, groupReceiveFileList);
        }
        //有时候相机会发两次过来，过滤掉
        for (int i = 0; i < groupReceiveFileList.size(); i++) {
            if (Objects.equals(groupReceiveFileList.get(i).getFileName(), filec.getFileName())) {
                LogUtil.d("收到重复文件，跳过");
                return;
            }
        }
        groupReceiveFileList.add(filec);
        isDownloadingFile = true;
        //开始下载
        AppExecutors.runOnIoThread(() -> {
            String path = getApplication().getCacheDir().getAbsolutePath();
            FileOper.createDirectory(path);

            LogUtil.d("开始下载：" + filec.getFileName());
            long time = System.currentTimeMillis();
            String localFile = path + "/" + filec.getFileName();
            boolean retValue = fileOperation.downloadFile(filec, localFile);
            LogUtil.d("下载" + (retValue ? "成功：" : "失败：") + filec.getFileName() + ":" + (System.currentTimeMillis() - time) / 1000 + "秒:" + FileUtils.getSize(new File(path + "/" + filec.getFileName())));

            if (retValue) {
                synchronized (groupName) {//加个线程锁，防止冲突
                    ArrayList<String> successList;
                    if (successDownloadMap.containsKey(groupName)) {
                        successList = successDownloadMap.get(groupName);
                    } else {
                        successList = new ArrayList<>();
                        successDownloadMap.put(groupName, successList);
                    }
                    successList.add(localFile);

                    if (successList.size() == 12) {
                        LogUtil.d("全部下载完成，开始HDR");
                        successDownloadMap.remove(groupName);
                        reciverFileMap.remove(groupName);
                        isDownloadingFile = false;

                        if (!TextUtils.isEmpty(currentRemarkName)) {
                            String key = SPKey.REMARK_NAME_PREFIX + groupName;
                            LogUtils.d("关联上备注名：" + key + "：" + currentRemarkName);
                            //关联上备注名
                            MMKV.defaultMMKV().putString(key, currentRemarkName);
                            currentRemarkName = null;
                            EventBus.getDefault().post(new RemarkClearEvent());
                        }
                        //通知拼接中
                        stitchingGroupList.add(groupName);
                        EventBus.getDefault().post(new BackgroundStitchingCountRefreshEvent(stitchingGroupList.size()));

                        executeHdr(successList);//todo 不用组的文件分不同的文件夹
                    }
                }
            }
        });
    }

    //根据文件名获取分组名称
    private String getGroupName(String fileName) {
        if (TextUtils.isEmpty(fileName)) {
            return "";
        }
        fileName = fileName.replace("fusion", "");
        if (fileName.contains("_")) {
            return fileName.substring(0, fileName.lastIndexOf("_"));
        } else {
            return "";
        }
    }

    /**
     * 清理所有拼接状态，用于异常情况下的状态重置
     */
    public static void clearAllStitchingStates() {
        if (!stitchingGroupList.isEmpty()) {
            LogUtil.w("清理所有拼接状态，数量：" + stitchingGroupList.size());
            stitchingGroupList.clear();
            EventBus.getDefault().post(new BackgroundStitchingCountRefreshEvent(0));
        }
    }

    public final static Pattern pattern = Pattern.compile("_[0-3]\\w{0,1}\\.");

    private static void executeHdr(ArrayList<String> hdrCacheFiles) {
        //开一个线程，跟activity 脱钩，保证下载完之后能执行完
        AppExecutors.runOnIoThread(() -> {//这里用进程锁，防止同时进行多个HDR 拼接
            long time = System.nanoTime();
            LogUtil.d("排队等待拼接：" + time);
            synchronized (TAG) {
                LogUtil.d("排队等待拼接结束，开始拼接：" + time);
                executeHdrWithNewThread(hdrCacheFiles);
            }
        });
    }

    private static void executeHdrWithNewThread(ArrayList<String> hdrCacheFiles) {
        // 提前获取groupName，用于状态清理
        String groupName = null;
        if (!hdrCacheFiles.isEmpty()) {
            String firstFilePath = hdrCacheFiles.get(0);
            String fileName = new File(firstFilePath).getName();
            // 使用现有的getGroupName逻辑
            fileName = fileName.replace("fusion", "");
            if (fileName.contains("_")) {
                groupName = fileName.substring(0, fileName.lastIndexOf("_"));
            }
        }

        ArrayList<String> fourCacheFiles = new ArrayList<>();
        for (int i = 0; i < hdrCacheFiles.size(); i++) {
            if (!TextUtils.isEmpty(hdrCacheFiles.get(i))) {
                if (hdrCacheFiles.get(i).contains("A")) {
                    fourCacheFiles.add(hdrCacheFiles.get(i));
                }
            }
        }
        //注释掉普通拼接，直接进行HDR 拼接
//        String tmpPano = "";
//        if (fourCacheFiles.size() == 4) {
//            long start = System.currentTimeMillis();
//            String path = fourCacheFiles.get(0);
//            Matcher matcher = pattern.matcher(path);
//            String cachePath = getApplication().getCacheDir().getAbsolutePath() + "/ijoyer";
//            FileUtils.createOrExistsDir(cachePath + "/ijoyer");
//            try {
//                InputStream isA = new FileInputStream(fourCacheFiles.get(3));
//                InputStream isB = new FileInputStream(fourCacheFiles.get(2));
//                InputStream isC = new FileInputStream(fourCacheFiles.get(1));
//                InputStream isD = new FileInputStream(fourCacheFiles.get(0));
//                File fileA = new File(cachePath, fourCacheFiles.get(3).substring(fourCacheFiles.get(3).lastIndexOf("/") + 1));
//                File fileB = new File(cachePath, fourCacheFiles.get(2).substring(fourCacheFiles.get(2).lastIndexOf("/") + 1));
//                File fileC = new File(cachePath, fourCacheFiles.get(1).substring(fourCacheFiles.get(1).lastIndexOf("/") + 1));
//                File fileD = new File(cachePath, fourCacheFiles.get(0).substring(fourCacheFiles.get(0).lastIndexOf("/") + 1));
//
//                StitchUtils.copyFile(fileA, isA);
//                StitchUtils.copyFile(fileB, isB);
//                StitchUtils.copyFile(fileC, isC);
//                StitchUtils.copyFile(fileD, isD);
//
//                matcher = pattern.matcher(new File(path).getName());
//                tmpPano = matcher.replaceFirst("_" + "PANO" + ".");
//
//                LogUtil.e("普通拼接开始");
//                int result = StitchUtils.stitchExec(PanoramaApp.getContext(), fileD.getAbsolutePath(), fileC.getAbsolutePath(), fileB.getAbsolutePath(), fileA.getAbsolutePath(), tmpPano);
//                long end = System.currentTimeMillis();
//                if (0 == result) {
//                    LogUtil.e("普通拼接成功,时间：" + (end - start) + "ms");
//                } else {
//                    LogUtil.e("普通拼接失败（" + result + "）");
//                }
//
//            } catch (FileNotFoundException e) {
//                e.printStackTrace();
//            }
//        }
//        LogUtil.e(GsonUtils.toJson(fourCacheFiles));
//        try {
//            Thread.sleep(500);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }


        LogUtil.e(GsonUtils.toJson(hdrCacheFiles));
        ArrayList<HdrBean> hdrBeans = PbDownloadManager.startHdr(hdrCacheFiles);
        if (hdrBeans.size() < 4) {
            // 修复：无论hdrBeans是否为空，都要清理状态
            if (!TextUtils.isEmpty(groupName)) {
                stitchingGroupList.remove(groupName);
                EventBus.getDefault().post(new BackgroundStitchingCountRefreshEvent(stitchingGroupList.size()));
                LogUtil.d("HDR失败，清理状态：" + groupName + "，hdrBeans.size=" + hdrBeans.size());
            }
            return;
        }

        long start = System.currentTimeMillis();
        String path = hdrBeans.get(0).mainPicPath;
        String filePathA = hdrBeans.get(0).mainPicPath;
        String filePathB = hdrBeans.get(1).mainPicPath;
        String filePathC = hdrBeans.get(2).mainPicPath;
        String filePathD = hdrBeans.get(3).mainPicPath;
        String filePathCaliText = hdrCacheFiles.get(hdrCacheFiles.size() - 3);
//        Matcher matcher = pbDownloadManager.pattern2.matcher(new File(path).getName());
//        String tmp = matcher.replaceFirst("_" + "HDR" + ".").replaceAll("fusion", "");
        String initName = new File(path).getName();
        String name = initName.replace("fusion", "");
        String tmp;
        if (!TextUtils.isEmpty(hdrBeans.get(0).nameId)) {
            tmp = name.substring(0, name.indexOf('.')).replace(hdrBeans.get(0).nameId, "");
        } else {
            int index = name.lastIndexOf("_");
            tmp = name.substring(0, index);
        }
        // 使用已经计算好的groupName，如果为空则使用tmp作为备用
        if (TextUtils.isEmpty(groupName)) {
            groupName = tmp.trim();
        }
        tmp += "_HDR.JPG";

        int result = StitchUtils.stitchExecHdr(PanoramaApp.getContext(), filePathA, filePathB, filePathC, filePathD, filePathCaliText, tmp);
        String finalTmp = tmp;
        long end = System.currentTimeMillis();
        if (0 == result) {
//            File file = new File(Environment.getExternalStorageDirectory() + AppInfo.DOWNLOAD_PATH_PHOTO + tmpPano);
//            if (FileUtils.isFileExists(file)) {
//                file.delete();
//            }
            LogUtil.e("HDR拼接成功,时间：" + (end - start) + "ms");

            EventBus.getDefault().post(new EventBusNotifyEntity(EventBusNotifyEntity.EVENT_BUS_NOTIFY));
        } else {
            LogUtil.e("HDR拼接失败（" + result + "）");
        }

        for (String temp : hdrCacheFiles) {
            File file = new File(temp);
            if (file.exists()) {
                file.delete();
                MediaRefresh.scanFileAsync(PanoramaApp.getContext(), temp);
            }
        }
        MediaRefresh.scanFileAsync(PanoramaApp.getContext(), Environment.getExternalStorageDirectory() + AppInfo.DOWNLOAD_PATH_PHOTO + tmp);

        for (HdrBean temp : hdrBeans) {
            for (String tempPath : temp.deletePath) {
                File file = new File(tempPath);
                if (file.exists()) {
                    file.delete();
                    MediaRefresh.scanFileAsync(PanoramaApp.getContext(), tempPath);
                }
            }
        }
        stitchingGroupList.remove(groupName);
        EventBus.getDefault().post(new BackgroundStitchingCountRefreshEvent(stitchingGroupList.size()));
    }

    public void addEvent() {
        sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_SDCARD_FULL);
        sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_BATTERY_LEVEL_CHANGED);
        sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_VIDEO_OFF);
        sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_VIDEO_ON);
        sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_CAPTURE_START);
        sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_CAPTURE_COMPLETE);
        sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_FILE_ADDED);
        sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_CONNECTION_DISCONNECTED);
        sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_TIMELAPSE_STOP);
        sdkEvent.addCustomizeEvent(0x5001);
        sdkEvent.addCustomizeEvent(0x400D);
        sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_FILE_DOWNLOAD);
        isDelEvent = false;
    }

    public synchronized void delEvent() {
        if (curCamera != null && curCamera.isConnected() && !isDelEvent) {
            sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_SDCARD_FULL);
            sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_BATTERY_LEVEL_CHANGED);
            sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_CAPTURE_COMPLETE);
            sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_CAPTURE_START);
            sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_VIDEO_OFF);
            sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_FILE_ADDED);
            sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_VIDEO_ON);
            sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_CONNECTION_DISCONNECTED);
            sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_TIMELAPSE_STOP);
            sdkEvent.delCustomizeEventListener(0x5001);
            sdkEvent.delCustomizeEventListener(0x400D);
            sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_FILE_DOWNLOAD);
            isDelEvent = true;
        }
    }

    public synchronized void loadSettingMenuList() {
        AppLog.i(TAG, "setupBtn is clicked:allowClickButtoms=" + allowClickButtoms);
        if (allowClickButtoms == false) {
            return;
        }
        if (!checkModeSwitch(curAppStateMode)) {
            int resId = getSwitchErrorResId(curAppStateMode);
            if (resId > 0) {
                MyToast.show(activity, resId);
            }
            return;
        }
        allowClickButtoms = false;
        if (curAppStateMode == PreviewMode.APP_STATE_STILL_PREVIEW) {
            previewView.setSetupMainMenuVisibility(View.VISIBLE);
            currentSettingMenuMode = UIDisplaySource.CAPTURE_SETTING_MENU;
            if (settingMenuList != null) {
                settingMenuList.clear();
            }
            if (settingListAdapter != null) {
                settingListAdapter.notifyDataSetChanged();
            }
            MyProgressDialog.showProgressDialog(activity, R.string.action_processing);
            new Thread(new Runnable() {
                @Override
                public void run() {
                    previewHandler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            settingMenuList = UIDisplaySource.getInstance().getList(UIDisplaySource.CAPTURE_SETTING_MENU, curCamera);
                            settingListAdapter = new SettingListAdapter(activity, settingMenuList, previewHandler);
                            previewView.setSettingMenuListAdapter(settingListAdapter);
                            MyProgressDialog.closeProgressDialog();
                        }
                    }, 500);
                }
            }).start();
        } else if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW) {
            previewView.setSetupMainMenuVisibility(View.VISIBLE);
            currentSettingMenuMode = UIDisplaySource.VIDEO_SETTING_MENU;
            MyProgressDialog.showProgressDialog(activity, R.string.action_processing);
            if (settingMenuList != null) {
                settingMenuList.clear();
            }
            if (settingListAdapter != null) {
                settingListAdapter.notifyDataSetChanged();
            }
            new Thread(new Runnable() {
                @Override
                public void run() {
                    previewHandler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            settingMenuList = UIDisplaySource.getInstance().getList(UIDisplaySource.VIDEO_SETTING_MENU, curCamera);
                            settingListAdapter = new SettingListAdapter(activity, settingMenuList, previewHandler);
                            previewView.setSettingMenuListAdapter(settingListAdapter);
                            MyProgressDialog.closeProgressDialog();
                        }
                    }, 500);
                }
            }).start();
        } else if (curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW || curAppStateMode == APP_STATE_TIMELAPSE_VIDEO_PREVIEW) {
            previewView.setSetupMainMenuVisibility(View.VISIBLE);
            currentSettingMenuMode = UIDisplaySource.TIMELAPSE_SETTING_MENU;
            MyProgressDialog.showProgressDialog(activity, R.string.action_processing);
            if (settingMenuList != null) {
                settingMenuList.clear();
            }
            if (settingListAdapter != null) {
                settingListAdapter.notifyDataSetChanged();
            }
            new Thread(new Runnable() {
                @Override
                public void run() {
                    previewHandler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            settingMenuList = UIDisplaySource.getInstance().getList(UIDisplaySource.TIMELAPSE_SETTING_MENU, curCamera);
                            settingListAdapter = new SettingListAdapter(activity, settingMenuList, previewHandler);
                            previewView.setSettingMenuListAdapter(settingListAdapter);
                            MyProgressDialog.closeProgressDialog();
                        }
                    }, 500);
                }
            }).start();
        }
        allowClickButtoms = true;
    }

    @Override
    public void isAppBackground() {
        super.isAppBackground();
    }

    @Override
    public void finishActivity() {
        Tristate ret = Tristate.NORMAL;
        if (previewView.getSetupMainMenuVisibility() == View.VISIBLE) {
            AppLog.i(TAG, "onKeyDown curAppStateMode==" + curAppStateMode);
            previewView.setSetupMainMenuVisibility(View.GONE);
            if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW) {
                AppLog.i(TAG, "onKeyDown curAppStateMode == APP_STATE_VIDEO_PREVIEW");
                changeCameraMode(curAppStateMode, ICatchCamPreviewMode.ICH_CAM_VIDEO_PREVIEW_MODE);
            } else if (curAppStateMode == PreviewMode.APP_STATE_STILL_PREVIEW) {
                changeCameraMode(curAppStateMode, ICatchCamPreviewMode.ICH_CAM_STILL_PREVIEW_MODE);
            } else if (curAppStateMode == APP_STATE_TIMELAPSE_VIDEO_PREVIEW) {
                AppLog.i(TAG, "onKeyDown curAppStateMode == APP_STATE_TIMELAPSE_PREVIEW_VIDEO");
                curCamera.timeLapsePreviewMode = TimeLapseMode.TIME_LAPSE_MODE_VIDEO;
                changeCameraMode(curAppStateMode, ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_VIDEO_PREVIEW_MODE);
            } else if (curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW) {
                AppLog.i(TAG, "onKeyDown curAppStateMode == APP_STATE_TIMELAPSE_PREVIEW_STILL");
                curCamera.timeLapsePreviewMode = TimeLapseMode.TIME_LAPSE_MODE_STILL;
                changeCameraMode(curAppStateMode, ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_STILL_PREVIEW_MODE);
            } else {
                startPreview();
                createUIByMode(curAppStateMode);
            }
        } else {
            savePvThumbnail();
            destroyPreview();
            super.finishActivity();
        }
    }

    @Override
    public void redirectToAnotherActivity(final Context context, final Class<?> cls) {
        AppLog.i(TAG, "pbBtn is clicked curAppStateMode=" + curAppStateMode);
        if (allowClickButtoms == false) {
            AppLog.i(TAG, "do not allow to response button clicking");
            return;
        }
        if (!checkModeSwitch(curAppStateMode)) {
            int resId = getSwitchErrorResId(curAppStateMode);
            if (resId > 0) {
                MyToast.show(activity, resId);
            }
            return;
        }
        allowClickButtoms = false;
        Boolean isSDCardExist = cameraProperties.isSDCardExist();
        if (isSDCardExist == null) {
            AppDialog.showDialogWarn(activity, R.string.dialog_connect_failed, false, () -> {
                if (activity != null && !activity.isFinishing()) {
                    activity.finish();
                }
            });
            allowClickButtoms = true;
            return;
        } else if (Boolean.FALSE.equals(isSDCardExist)) {
            AppDialog.showDialogWarn(activity, R.string.dialog_card_lose);
            allowClickButtoms = true;
            return;
        }
        AppLog.i(TAG, "curAppStateMode =" + curAppStateMode);
        destroyPreview();
        delEvent();
        allowClickButtoms = true;
        MyProgressDialog.showProgressDialog(context, R.string.action_processing);
        previewHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                MyProgressDialog.closeProgressDialog();
                Intent intent = new Intent();
                AppLog.i(TAG, "intent:start PbMainActivity.class");
                intent.setClass(context, cls);
                context.startActivity(intent);
                AppLog.i(TAG, "intent:end start PbMainActivity.class");
            }
        }, 500);
        allowClickButtoms = true;
        AppLog.i(TAG, "end processing for responsing pbBtn clicking");
    }

    private boolean checkModeSwitch(int appStateMode) {
        if (appStateMode == PreviewMode.APP_STATE_VIDEO_CAPTURE || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE || curAppStateMode == PreviewMode.APP_STATE_STILL_CAPTURE || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE) {
            return false;
        } else {
            return true;
        }
    }

    private int getSwitchErrorResId(int appStateMode) {
        if (appStateMode == PreviewMode.APP_STATE_VIDEO_CAPTURE || appStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE) {
            return R.string.stream_error_recording;
        } else if (appStateMode == PreviewMode.APP_STATE_STILL_CAPTURE || appStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE) {
            return R.string.stream_error_capturing;
        } else {
            return -1;
        }
    }

    private class WifiSSReceiver extends BroadcastReceiver {
        private WifiManager wifi;

        public WifiSSReceiver() {
            super();
            wifi = (WifiManager) activity.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            changeWifiStatusIcon();
        }

        @Override
        public void onReceive(Context arg0, Intent arg1) {
            changeWifiStatusIcon();
        }

        private void changeWifiStatusIcon() {
            WifiInfo info = wifi.getConnectionInfo();
            if (info.getBSSID() != null) {
                int strength = WifiManager.calculateSignalLevel(info.getRssi(), 8);
                AppLog.d(TAG, "change Wifi Status：" + strength);
                switch (strength) {
                    case 0:
                        previewView.setWifiIcon(R.drawable.ic_signal_wifi_0_bar_green_24dp);
                        break;
                    case 1:
                        previewView.setWifiIcon(R.drawable.ic_signal_wifi_1_bar_green_24dp);
                        break;
                    case 2:
                    case 3:
                        previewView.setWifiIcon(R.drawable.ic_signal_wifi_2_bar_green_24dp);
                        break;
                    case 4:
                    case 5:
                        previewView.setWifiIcon(R.drawable.ic_signal_wifi_3_bar_green_24dp);
                        break;
                    case 6:
                    case 7:
                        previewView.setWifiIcon(R.drawable.ic_signal_wifi_4_bar_green_24dp);
                        break;
                    default:
                        break;
                }
            }
        }
    }

    public void startPreview() {
        AppLog.d(TAG, "start startPreview hasInitSurface=" + hasInitSurface);
        if (!hasInitSurface) {
            return;
        }
        if (panoramaPreviewPlayback == null) {
            AppLog.d(TAG, "null point");
            return;
        }
        if (curCamera.isStreamReady) {
            return;
        }

//        if (!checkNativeMediaCodecType()){
//            return;
//        }

//        printTestResource();
        boolean isSupportPreview = cameraProperties.isSupportPreview();
        AppLog.d(TAG, "start startPreview isSupportPreview=" + isSupportPreview);
        if (!isSupportPreview) {
            previewHandler.post(() -> previewView.setSupportPreviewTxvVisibility(View.VISIBLE));
            return;
        }
        if (AppInfo.enableDumpVideo) {
            String streamOutputPath = Environment.getExternalStorageDirectory().toString() + AppInfo.STREAM_OUTPUT_DIRECTORY_PATH;
            FileOper.createDirectory(streamOutputPath);
            try {
                ICatchPancamConfig.getInstance().enableDumpTransportStream(true, streamOutputPath);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        int cacheTime = cameraProperties.getPreviewCacheTime();
        cacheTime = 400;
        AppLog.d(TAG, "setPreviewCacheParam cacheTime:" + cacheTime);
        ICatchPancamConfig.getInstance().setPreviewCacheParam(cacheTime, 200);
        ICatchStreamParam iCatchStreamParam = getStreamParam();
        final Tristate retValue;
        if (AppInfo.enableRender) {
            if (PanoramaTools.isPanorama(iCatchStreamParam.getWidth(), iCatchStreamParam.getHeight())) {
                registerGyroscopeSensor();
            }
            //这里之后，会崩溃，无法预料
            retValue = panoramaPreviewPlayback.start(iCatchStreamParam, !AppInfo.disableAudio);
        } else {
            retValue = cameraStreaming.start(iCatchStreamParam, !AppInfo.disableAudio);
        }
        if (retValue == Tristate.NORMAL) {
            curCamera.isStreamReady = true;
        } else {
            curCamera.isStreamReady = false;
        }
        previewHandler.post(new Runnable() {
            @Override
            public void run() {
                if (retValue == Tristate.ABNORMAL) {
                    previewView.setSupportPreviewTxvVisibility(View.VISIBLE);
                } else if (retValue == Tristate.NORMAL) {
                    previewView.setSupportPreviewTxvVisibility(View.GONE);
                } else {
                    previewView.setSupportPreviewTxvVisibility(View.GONE);
                    MyToast.show(activity, R.string.open_preview_failed);
                }
            }
        });
        AppLog.d(TAG, "end startPreview retValue=" + retValue);
    }

    ///打印当前系统资源
    private void printTestResource() {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long allocatedMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = allocatedMemory - freeMemory;
        LogUtils.file("printTestResource：Java Heap: Used=" + (usedMemory / 1024) + "KB, Allocated=" + (allocatedMemory / 1024) + "KB, Free=" + (freeMemory / 1024) + "KB, Max=" + (maxMemory / 1024) + "KB");

        long nativeHeapAllocatedSize = android.os.Debug.getNativeHeapAllocatedSize();
        long nativeHeapFreeSize = android.os.Debug.getNativeHeapFreeSize();
        long nativeHeapSize = android.os.Debug.getNativeHeapSize(); // 总大小
        LogUtils.file("printTestResource：Native Heap: Allocated=" + (nativeHeapAllocatedSize / 1024) + "KB, Free=" + (nativeHeapFreeSize / 1024) + "KB, TotalSize=" + (nativeHeapSize / 1024) + "KB");
        LogUtils.file("printTestResource：Thread Num: " + Thread.activeCount());
    }

    private ICatchStreamParam getStreamParam() {
        StreamInfo streamInfo = null;
        if (curCamera.getCameraType() == CameraType.USB_CAMERA) {
            streamInfo = new StreamInfo(curCodecType, curVideoWidth, curVideoHeight, 5000000, curVideoFps);
            AppLog.d(TAG, "start startPreview videoWidth=" + curVideoWidth + " videoHeight=" + curVideoHeight + " videoFps=" + curVideoFps + " curCodecType=" + curCodecType);
        } else {
            String streamUrl = cameraProperties.getCurrentStreamInfo();
            AppLog.d(TAG, " start startStreamAndPreview streamUrl=[" + streamUrl + "]");
            if (streamUrl != null) {
                streamInfo = StreamInfoConvert.convertToStreamInfoBean(streamUrl);
            } else {
                LogUtils.file("startPewview()-getStreamParam()：streamUrl==null");
            }
        }
        ICatchStreamParam iCatchStreamParam = null;
        if (streamInfo == null) {
            iCatchStreamParam = new ICatchH264StreamParam(1280, 720, 30);
        } else if (streamInfo.mediaCodecType.equals("MJPG")) {
            iCatchStreamParam = new ICatchJPEGStreamParam(streamInfo.width, streamInfo.height, streamInfo.fps, streamInfo.bitrate);
        } else if (streamInfo.mediaCodecType.equals("H264")) {
            iCatchStreamParam = new ICatchH264StreamParam(streamInfo.width, streamInfo.height, streamInfo.fps, streamInfo.bitrate);
        } else {
            iCatchStreamParam = new ICatchH264StreamParam(1280, 720, 30);
        }
        return iCatchStreamParam;
    }

    public void stopPreview() {
        if (AppInfo.enableDumpVideo) {
            ICatchPancamConfig.getInstance().disableDumpTransportStream(true);
        }
        if (AppInfo.enableRender) {
            removeGyroscopeListener();
            if (panoramaPreviewPlayback != null && curCamera.isStreamReady) {
                curCamera.isStreamReady = false;
                panoramaPreviewPlayback.stop();
            }
        } else {
            if (curCamera.isStreamReady) {
                curCamera.isStreamReady = false;
                cameraStreaming.stop();
            }
        }
    }

    public void locate(float progerss) {
        panoramaPreviewPlayback.locate(progerss);
    }

    public void savePvThumbnail() {
        if (curCamera != null && panoramaPreviewPlayback != null && curCamera.isStreamReady) {
            Bitmap bitmap = panoramaPreviewPlayback.getPvThumbnail();
            if (bitmap != null) {
                ByteArrayOutputStream output = new ByteArrayOutputStream();
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, output);
                byte[] result = output.toByteArray();
                try {
                    output.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                CameraSlotSQLite.getInstance().update(new CameraSlot(curCamera.getPosition(), true, curCamera.getCameraName(), curCamera.getCameraType(), result, true));
            }
        }
    }

    public void destroyPreview() {
        if (AppInfo.enableDumpVideo) {
            ICatchPancamConfig.getInstance().disableDumpTransportStream(true);
        }
        hasInitSurface = false;
        if (AppInfo.enableRender) {
            removeGyroscopeListener();
            if (panoramaPreviewPlayback != null && curCamera.isStreamReady) {
                if (iCatchSurfaceContext != null) {
                    AppLog.d(TAG, "destroyPreview.....");
                    panoramaPreviewPlayback.removeSurface(ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_SPHERE, iCatchSurfaceContext);
                }
                panoramaPreviewPlayback.stop();
                panoramaPreviewPlayback.release();
                curCamera.isStreamReady = false;
            }
        } else {
            if (curCamera.isStreamReady) {
                curCamera.isStreamReady = false;
                cameraStreaming.stop();
            }
        }
    }

    public void rotate(ICatchGLPoint prev, ICatchGLPoint next) {
        panoramaPreviewPlayback.rotate(prev, next);
    }

    public void rotateB(MotionEvent e, float prevX, float prevY) {
        ICatchGLPoint prev = new ICatchGLPoint(prevX, prevY);
        ICatchGLPoint curr = new ICatchGLPoint(e.getX(), e.getY());
        panoramaPreviewPlayback.rotate(prev, curr);
    }

    public void onSufaceViewTouchDown(MotionEvent event) {
        touchMode = TouchMode.DRAG;
        mPreviousY = event.getY();
        mPreviousX = event.getX();
        beforeLenght = 0;
        afterLenght = 0;
    }

    public void onSufaceViewPointerDown(MotionEvent event) {
        if (event.getPointerCount() == 2) {
            touchMode = TouchMode.ZOOM;
            beforeLenght = getDistance(event);
        }
    }

    public void onSufaceViewTouchMove(MotionEvent event) {
        if (touchMode == TouchMode.DRAG) {
            rotateB(event, mPreviousX, mPreviousY);
            mPreviousY = event.getY();
            mPreviousX = event.getX();
        } else if (touchMode == TouchMode.ZOOM) {
            afterLenght = getDistance(event);
            float gapLenght = afterLenght - beforeLenght;
            if (Math.abs(gapLenght) > 5f) {
                float scale_temp = afterLenght / beforeLenght;
                this.setScale(scale_temp);
                beforeLenght = afterLenght;
            }
        }
    }

    float getDistance(MotionEvent event) {
        float x = event.getX(0) - event.getX(1);
        float y = event.getY(0) - event.getY(1);
        return (float) StrictMath.sqrt(x * x + y * y);
    }

    void setScale(float scale) {
        if ((currentZoomRate >= MAX_ZOOM && scale > 1) || (currentZoomRate <= MIN_ZOOM && scale < 1)) {
            return;
        }
        float temp = currentZoomRate * scale;
        if (scale > 1) {
            if (temp <= MAX_ZOOM) {
                currentZoomRate = currentZoomRate * scale;
                zoom(currentZoomRate);
            } else {
                currentZoomRate = MAX_ZOOM;
                zoom(currentZoomRate);
            }
        } else if (scale < 1) {
            if (temp >= MIN_ZOOM) {
                currentZoomRate = currentZoomRate * scale;
                zoom(currentZoomRate);
            } else {
                currentZoomRate = MIN_ZOOM;
                zoom(currentZoomRate);
            }
        }
    }

    private void zoom(float currentZoomRate) {
        locate(1 / currentZoomRate);
    }

    public void onSufaceViewTouchUp() {
        showZoomView();
        touchMode = TouchMode.NONE;
    }

    public void onSufaceViewTouchPointerUp() {
        touchMode = TouchMode.NONE;
    }

    @Override
    public void onSensorChanged(SensorEvent event) {
        if (event.sensor == null) {
            return;
        }
        if (event.sensor.getType() == Sensor.TYPE_GYROSCOPE) {
            float speedX = event.values[0];
            float speedY = event.values[1];
            float speedZ = event.values[2];
            if (Math.abs(speedY) < 0.05 && Math.abs(speedZ) < 0.05) {
                return;
            }
            rotate(speedX, speedY, speedZ, event.timestamp);
        }
    }

    @Override
    public void onAccuracyChanged(Sensor sensor, int accuracy) {
    }

    private void rotate(float speedX, float speedY, float speedZ, long timestamp) {
        int rotation = activity.getWindowManager().getDefaultDisplay().getRotation();
        panoramaPreviewPlayback.rotate(rotation, speedX, speedY, speedZ, timestamp);
    }

    private void registerGyroscopeSensor() {
        AppLog.d(TAG, "registerGyroscopeSensor");
        sensorManager = (SensorManager) activity.getSystemService(Context.SENSOR_SERVICE);
        gyroscopeSensor = sensorManager.getDefaultSensor(Sensor.TYPE_GYROSCOPE);
        sensorManager.registerListener(this, gyroscopeSensor, SensorManager.SENSOR_DELAY_GAME);
    }

    protected void removeGyroscopeListener() {
        if (sensorManager != null) {
            sensorManager.unregisterListener(this);
        }
    }

    public void setDrawingArea(int width, int height) {
        if (panoramaPreviewPlayback != null && iCatchSurfaceContext != null) {
            AppLog.d(TAG, "start setDrawingArea width=" + width + " height=" + height);
            try {
                iCatchSurfaceContext.setViewPort(0, 0, width, height);
            } catch (IchGLSurfaceNotSetException e) {
                e.printStackTrace();
            }
            AppLog.d(TAG, "end setDrawingArea");
        }
    }

    public void initSurface(SurfaceHolder surfaceHolder) {
//        if (hasInitSurface){
//            LogUtils.d("不要重复初始化了");
//            return;
//        }
        hasInitSurface = false;
        AppLog.i(TAG, "begin initSurface");
        if (panoramaPreviewPlayback == null) {
            return;
        }
        if (AppInfo.enableRender) {
            iCatchSurfaceContext = new ICatchSurfaceContext(surfaceHolder.getSurface());
            ICatchStreamParam iCatchStreamParam = getStreamParam();
            AppLog.i(TAG, "iCatchStreamParam.getWidth() = " + iCatchStreamParam.getWidth());
            AppLog.i(TAG, "iCatchStreamParam.getHeight() = " + iCatchStreamParam.getHeight());
//            iCatchSurfaceContext.setViewPort(90, 90, iCatchStreamParam.getWidth(), iCatchStreamParam.getHeight());
            if (iCatchStreamParam != null && PanoramaTools.isPanorama(iCatchStreamParam.getWidth(), iCatchStreamParam.getHeight())) {
                panoramaPreviewPlayback.enableGLRender();
                panoramaPreviewPlayback.init(ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_SPHERE);
                panoramaPreviewPlayback.setSurface(ICatchGLSurfaceType.ICH_GL_SURFACE_TYPE_SPHERE, iCatchSurfaceContext);
                previewView.setPanoramaTypeBtnVisibility(View.VISIBLE);
            } else {
                panoramaPreviewPlayback.enableCommonRender(iCatchSurfaceContext);
                previewView.setPanoramaTypeBtnVisibility(View.GONE);
            }
        } else {
            previewView.setPanoramaTypeBtnVisibility(View.GONE);
            cameraStreaming.disnableRender();
            int width = previewView.getSurfaceViewWidth();
            int heigth = previewView.getSurfaceViewHeight();
            AppLog.i(TAG, "SurfaceViewWidth=" + width + " SurfaceViewHeight=" + heigth);
            if (width <= 0 || heigth <= 0) {
                width = 1080;
                heigth = 1920;
            }
            cameraStreaming.setSurface(surfaceHolder);
            cameraStreaming.setViewParam(width, heigth);
        }
        hasInitSurface = true;
        AppLog.i(TAG, "end initSurface");
    }

    public void showSharedUrlDialog(final Context context, final String shareUrl) {
        androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(context);
        View view = LayoutInflater.from(context).inflate(R.layout.live_shared_url, null);
        final EditText resetTxv = (EditText) view.findViewById(R.id.shared_url);
        final ImageView qrcodeImage = (ImageView) view.findViewById(R.id.shared_url_qrcode);
        Bitmap bitmap = QRCode.createQRCodeWithLogo(shareUrl, QRCode.WIDTH, BitmapFactory.decodeResource(context.getResources(), R.drawable.ic_panorama_green_500_48dp));
        qrcodeImage.setImageBitmap(bitmap);
        resetTxv.setText(shareUrl);
        builder.setTitle("Success, share url is:");
        builder.setPositiveButton(R.string.ok, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
            }
        });
        builder.setView(view);
        builder.setCancelable(false);
        builder.create().show();
    }


    private int count = 0;

    public void setPanoramaType() {
        if (curPanoramaType == ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_SPHERE) {
            panoramaPreviewPlayback.changePanoramaType(ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_ASTEROID);
            curPanoramaType = ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_ASTEROID;
            previewView.setPanoramaTypeBtnSrc(R.drawable.asteroid);
            activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_SENSOR);
        } else if (curPanoramaType == ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_ASTEROID) {
            panoramaPreviewPlayback.changePanoramaType(ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_VIRTUAL_R);
            curPanoramaType = ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_VIRTUAL_R;
            previewView.setPanoramaTypeBtnSrc(R.drawable.vr);
            activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE);
        } else {
            panoramaPreviewPlayback.changePanoramaType(ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_SPHERE);
            curPanoramaType = ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_SPHERE;
            previewView.setPanoramaTypeBtnSrc(R.drawable.panorama);
            activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_SENSOR);
        }
    }

    /// 相机当前变量的缓存<propertyId,PropertyValueCache>
    final Map<Integer, PropertyValueCache> propertyCacheMap = new HashMap<>();

    // 获取相机当前变量，带缓存，100ms 只获取一次
    //统计到每次获取需要10 ms 左右，巅峰期几ms 就去获取一次，不需要那么频繁去获取，减少系统开销
    //默认重新获取，不然有些状态会因为缓存导致混乱，只有在确定没影响时才使用缓存
    private int getCurrentPropertyValue(int propertyId) {
        return getCurrentPropertyValue(propertyId, true);
    }

    private int getCurrentPropertyValue(int propertyId, boolean isFocus) {
        PropertyValueCache cache = propertyCacheMap.get(propertyId);
        if (cache != null) {
            if (!isFocus && cache.value != null && (System.currentTimeMillis() - cache.time) < 200) {
                return cache.value;
            }
        } else {
            cache = new PropertyValueCache();
            propertyCacheMap.put(propertyId, cache);
        }
        int value = cameraProperties.getCurrentPropertyValue(propertyId);
        cache.value = value;
        cache.time = System.currentTimeMillis();
        if (cache.value == 10) {
            LogUtils.file("收到固件发过来的测试状态：0XD761=10");
        }
        return value;
    }

    private static class PropertyValueCache {
        Integer value;
        ///获取时间
        long time;
    }

    /**
     * A6Max专用录像开始处理方法
     * 按照流程图逻辑实现
     */
    private void startA6MaxVideoRecord(int duration) {
        AppLog.i(TAG, "开始A6Max录像处理流程");
        LogUtils.file("A6Max录像：开始录像处理流程");



        new Thread(() -> {
            try {
                // 步骤1: 检查A6Max特殊状态（0xD763）
                String errStr = checkA6MaxSpecialStatus();
                if (!TextUtils.isEmpty(errStr)) {
                    previewHandler.post(() -> {
                        MyProgressDialog.closeProgressDialog();
                        // 重置A6Max录像相关状态
                        resetA6MaxRecordState();
                        AppDialog.showDialogWarn(activity, errStr);
                    });
                    return;
                }

                // 步骤2: 播放开始音效
                videoCaptureStartBeep.start();

                // 步骤4: 开始录像
                lastRecodeTime = System.currentTimeMillis();
                isA6MaxRecordProcessing = true;

                final boolean ret = startA6MaxRecordProcess();

                previewHandler.post(() -> {
                    MyProgressDialog.closeProgressDialog();
                    if (ret) {
                        AppLog.i(TAG, "A6Max录像开始成功");
                        LogUtils.file("A6Max录像：录像开始成功");
                        curAppStateMode = PreviewMode.APP_STATE_VIDEO_CAPTURE;
                        startVideoCaptureButtomChangeTimer();
                        startRecordingLapseTimeTimer(0);
                        startA6MaxRecordMonitor(); // 开始监控录像状态
                    } else {
                        AppLog.e(TAG, "A6Max录像开始失败");
                        LogUtils.file("A6Max录像：录像开始失败");
                        MyToast.show(activity, "A6Max录像开始失败");
                        // 重置A6Max录像相关状态
                        resetA6MaxRecordState();
                    }
                });

            } catch (Exception e) {
                LogUtils.e(TAG, "A6Max录像处理异常", e);
                LogUtils.file("A6Max录像：处理异常 - " + e.getMessage());
                previewHandler.post(() -> {
                    MyProgressDialog.closeProgressDialog();
                    MyToast.show(activity, "A6Max录像处理异常");
                    // 重置A6Max录像相关状态
                    resetA6MaxRecordState();
                });
            }
        }).start();
    }

    /**
     * 检查A6Max特殊状态（0xD763状态码）
     */
    private String checkA6MaxSpecialStatus() {
        LogUtils.d(TAG, "检查A6Max特殊状态");
        LogUtils.file("A6Max录像：检查特殊状态");

        try {
            // 检查是否支持A6Max录像状态属性
            if (!cameraProperties.hasFunction(PropertyId.A6MAX_RECORD_STATUS)) {
                LogUtils.w(TAG, "A6Max不支持录像状态检查");
                LogUtils.file("A6Max录像：不支持录像状态检查");
                return "A6Max录像：不支持录像"; // 如果不支持，则跳过检查
            }

            // 获取A6Max录像状态
            int recordStatus = getCurrentPropertyValue(PropertyId.A6MAX_RECORD_STATUS);
            LogUtils.d(TAG, "A6Max录像状态: " + recordStatus + " (" + A6MaxRecordStatus.getStatusDescription(recordStatus) + ")");
            LogUtils.file("A6Max录像：当前状态 - " + recordStatus + " (" + A6MaxRecordStatus.getStatusDescription(recordStatus) + ")");

            // 检查是否可以开始录像
            if (!A6MaxRecordStatus.canStartRecord(recordStatus)) {
                LogUtils.w(TAG, "A6Max当前状态不允许开始录像: " + A6MaxRecordStatus.getStatusDescription(recordStatus));
                LogUtils.file("A6Max录像：当前状态不允许开始录像 - " + A6MaxRecordStatus.getStatusDescription(recordStatus));

                return A6MaxRecordStatus.getStatusDescription(recordStatus);
            }

            LogUtils.d(TAG, "A6Max特殊状态检查通过");
            LogUtils.file("A6Max录像：特殊状态检查通过");
            return null;

        } catch (Exception e) {
            LogUtils.e(TAG, "A6Max状态检查异常", e);
            LogUtils.file("A6Max录像：状态检查异常 - " + e.getMessage());

            // 异常情况下显示统一的错误提示
            return "A6Max状态检查失败，请检查相机连接";
        }
    }

    /**
     * 重置A6Max录像相关状态
     * 参考其他相机录像失败时的处理方式
     */
    private void resetA6MaxRecordState() {
        AppLog.d(TAG, "重置A6Max录像相关状态");
        LogUtils.file("A6Max录像：重置录像相关状态");

        try {
            // 重置录像处理标志
            isA6MaxRecordProcessing = false;

            // 重置应用状态模式为预览模式
            curAppStateMode = PreviewMode.APP_STATE_VIDEO_PREVIEW;

            // 停止录像相关的定时器
            stopVideoCaptureButtomChangeTimer();
            stopRecordingLapseTimeTimer();
            stopA6MaxRecordMonitor();

            // 恢复录像按钮状态
            previewView.setCaptureBtnEnAbility(true);
            previewView.setCaptureBtnBackgroundResource(R.drawable.video_recording_btn_on);

            // 更新剩余录像时间显示
            previewView.setRemainRecordingTimeText(
                    ConvertTools.secondsToMinuteOrHours(cameraProperties.getRecordingRemainTime()));

            AppLog.d(TAG, "A6Max录像状态重置完成");
            LogUtils.file("A6Max录像：状态重置完成");

        } catch (Exception e) {
            LogUtils.e(TAG, "A6Max录像状态重置异常", e);
            LogUtils.file("A6Max录像：状态重置异常 - " + e.getMessage());
        }
    }

    /**
     * 开始A6Max录像处理
     */
    private boolean startA6MaxRecordProcess() {
        LogUtils.d(TAG, "开始A6Max录像处理");
        LogUtils.file("A6Max录像：开始录像处理");

        try {
            // 调用底层录像接口
            boolean ret = cameraAction.startMovieRecord();

            if (ret) {
                LogUtils.d(TAG, "A6Max底层录像启动成功");
                LogUtils.file("A6Max录像：底层录像启动成功");

                // A6Max特殊处理逻辑
                handleA6MaxSpecialRecordLogic();

                return true;
            } else {
                LogUtils.e(TAG, "A6Max底层录像启动失败");
                LogUtils.file("A6Max录像：底层录像启动失败");
                return false;
            }
        } catch (Exception e) {
            LogUtils.e(TAG, "A6Max录像处理异常", e);
            LogUtils.file("A6Max录像：录像处理异常 - " + e.getMessage());
            return false;
        }
    }

    /**
     * A6Max特殊录像逻辑处理
     */
    private void handleA6MaxSpecialRecordLogic() {
        LogUtils.d(TAG, "处理A6Max特殊录像逻辑");
        LogUtils.file("A6Max录像：处理特殊录像逻辑");

        // 根据流程图实现A6Max特殊的录像处理逻辑
        // 例如：特殊的编码设置、文件格式处理等
    }

    /**
     * 开始A6Max录像状态监控
     */
    private void startA6MaxRecordMonitor() {
        LogUtils.d(TAG, "开始A6Max录像状态监控");
        LogUtils.file("A6Max录像：开始状态监控");

        if (a6MaxRecordMonitorTimer != null) {
            a6MaxRecordMonitorTimer.cancel();
        }

        a6MaxRecordMonitorTimer = new Timer(true);
        TimerTask monitorTask = new TimerTask() {
            @Override
            public void run() {
                if (isA6MaxRecordProcessing) {
                    monitorA6MaxRecordStatus();
                }
            }
        };

        // 每5秒检查一次录像状态
        a6MaxRecordMonitorTimer.schedule(monitorTask, 5000, 5000);
    }

    /**
     * 监控A6Max录像状态
     */
    private void monitorA6MaxRecordStatus() {
        try {
            // 检查A6Max特殊状态（0xD763）
            if (cameraProperties.hasFunction(PropertyId.A6MAX_RECORD_STATUS)) {
                int recordStatus = cameraProperties.getCurrentPropertyValue(PropertyId.A6MAX_RECORD_STATUS);
                LogUtils.d(TAG, "A6Max录像监控状态: " + recordStatus + " (" + A6MaxRecordStatus.getStatusDescription(recordStatus) + ")");

                // 处理错误状态
                if (A6MaxRecordStatus.isErrorStatus(recordStatus)) {
                    LogUtils.e(TAG, "A6Max录像出现错误状态: " + A6MaxRecordStatus.getStatusDescription(recordStatus));
                    LogUtils.file("A6Max录像：出现错误状态 - " + A6MaxRecordStatus.getStatusDescription(recordStatus));

                    previewHandler.post(() -> {
                        stopA6MaxRecordMonitor();
                        handleA6MaxRecordErrorUnified(A6MaxErrorType.STATUS_ERROR, recordStatus, null);
                    });
                    return;
                }

                // 检查是否需要禁用录像按钮
                if (A6MaxRecordStatus.shouldDisableRecordButton(recordStatus)) {
                    previewHandler.post(() -> {
                        previewView.setCaptureBtnEnAbility(false);
                    });
                } else {
                    previewHandler.post(() -> {
                        previewView.setCaptureBtnEnAbility(true);
                    });
                }
            }

            // 检查录像状态
//            if (cameraState != null && !cameraState.isMovieRecording()) {
//                LogUtils.w(TAG, "A6Max录像意外停止");
//                LogUtils.file("A6Max录像：录像意外停止");
//
//                previewHandler.post(() -> {
//                    stopA6MaxRecordMonitor();
//                    handleA6MaxRecordErrorUnified(A6MaxErrorType.UNEXPECTED_STOP, -1, null);
//                });
//                return;
//            }

            // 检查存储空间
            int remainTime = cameraProperties.getRecordingRemainTime();
            if (remainTime > 0 && remainTime <= 30) { // 剩余时间少于30秒且大于0时警告
                LogUtils.w(TAG, "A6Max录像剩余时间不足: " + remainTime);
                LogUtils.file("A6Max录像：剩余时间不足 - " + remainTime);

                previewHandler.post(() -> {
                    MyToast.show(activity, "录像剩余时间不足: " + remainTime + "秒");
                });
            } else if (remainTime <= 0) {
                // 剩余时间为负数或0时，记录但不显示警告（可能是录像已结束）
                LogUtils.d(TAG, "A6Max录像剩余时间异常或已结束: " + remainTime);
                LogUtils.file("A6Max录像：剩余时间异常或已结束 - " + remainTime);
            }

        } catch (Exception e) {
            LogUtils.e(TAG, "A6Max录像状态监控异常", e);
            LogUtils.file("A6Max录像：状态监控异常 - " + e.getMessage());

            previewHandler.post(() -> {
                stopA6MaxRecordMonitor();
                handleA6MaxRecordErrorUnified(A6MaxErrorType.MONITOR_EXCEPTION, -1, e);
            });
        }
    }

    /**
     * 停止A6Max录像状态监控
     */
    private void stopA6MaxRecordMonitor() {
        LogUtils.d(TAG, "停止A6Max录像状态监控");
        LogUtils.file("A6Max录像：停止状态监控");

        if (a6MaxRecordMonitorTimer != null) {
            a6MaxRecordMonitorTimer.cancel();
            a6MaxRecordMonitorTimer = null;
        }
    }

    /**
     * A6Max错误类型枚举
     */
    private enum A6MaxErrorType {
        STATUS_ERROR,       // 状态错误
        UNEXPECTED_STOP,    // 意外停止
        MONITOR_EXCEPTION   // 监控异常
    }

    /**
     * 统一处理A6Max录像错误
     * 合并了原来分散的错误处理逻辑
     */
    private void handleA6MaxRecordErrorUnified(A6MaxErrorType errorType, int errorStatus, Exception exception) {
        LogUtils.e(TAG, "A6Max录像错误统一处理: " + errorType + ", 状态: " + errorStatus);
        LogUtils.file("A6Max录像：错误统一处理 - " + errorType + ", 状态: " + errorStatus);

        // 统一重置录像状态
        isA6MaxRecordProcessing = false;
        curAppStateMode = PreviewMode.APP_STATE_VIDEO_PREVIEW;
        stopVideoCaptureButtomChangeTimer();
        stopRecordingLapseTimeTimer();

        // 根据错误类型确定错误消息
        String errorMessage = getA6MaxErrorMessage(errorType, errorStatus, exception);

        // 显示错误提示
        MyToast.show(activity, errorMessage);

        // 更新UI显示
        previewView.setRemainRecordingTimeText(
                ConvertTools.secondsToMinuteOrHours(cameraProperties.getRecordingRemainTime()));

        // 恢复录像按钮状态
        previewView.setCaptureBtnEnAbility(true);
        previewView.setCaptureBtnBackgroundResource(R.drawable.video_recording_btn_on);

        LogUtils.d(TAG, "A6Max录像错误处理完成");
        LogUtils.file("A6Max录像：错误处理完成");
    }

    /**
     * 根据错误类型和状态获取相应的错误消息
     */
    private String getA6MaxErrorMessage(A6MaxErrorType errorType, int errorStatus, Exception exception) {
        switch (errorType) {
            case STATUS_ERROR:
                // 根据具体的错误状态返回不同的提示
                switch (errorStatus) {
                    case A6MaxRecordStatus.ERROR_SD_CARD_FULL:
                        return "录像卡顿，已停止录像";
                    case A6MaxRecordStatus.ERROR_SD_CARD_REMOVED:
                        return "SD卡被拔出，录像已停止";
                    case A6MaxRecordStatus.ERROR_WIFI_DISCONNECTED:
                        return "WiFi连接断开，录像已停止";
                    default:
                        return "录像出现错误，已停止录像：" + A6MaxRecordStatus.getStatusDescription(errorStatus);
                }

            case UNEXPECTED_STOP:
                return "A6Max录像意外停止";

            case MONITOR_EXCEPTION:
                return "A6Max录像监控异常：" + (exception != null ? exception.getMessage() : "未知异常");

            default:
                return "A6Max录像出现未知错误";
        }
    }

    /**
     * 处理A6Max录像错误状态
     * @deprecated 使用 handleA6MaxRecordErrorUnified 替代
     */
    private void handleA6MaxRecordError(int errorStatus) {
        LogUtils.e(TAG, "处理A6Max录像错误: " + A6MaxRecordStatus.getStatusDescription(errorStatus));
        LogUtils.file("A6Max录像：处理错误状态 - " + A6MaxRecordStatus.getStatusDescription(errorStatus));

        // 调用统一的错误处理方法
        handleA6MaxRecordErrorUnified(A6MaxErrorType.STATUS_ERROR, errorStatus, null);
    }

    /**
     * 处理A6Max录像意外停止
     * @deprecated 使用 handleA6MaxRecordErrorUnified 替代
     */
    private void handleA6MaxRecordUnexpectedStop() {
        LogUtils.w(TAG, "处理A6Max录像意外停止");
        LogUtils.file("A6Max录像：处理意外停止");

        // 调用统一的错误处理方法
        handleA6MaxRecordErrorUnified(A6MaxErrorType.UNEXPECTED_STOP, -1, null);
    }

    /**
     * A6Max专用录像停止处理方法
     * 按照流程图逻辑实现
     */
    private void stopA6MaxVideoRecord() {
        LogUtils.i(TAG, "开始A6Max录像停止处理流程");
        LogUtils.file("A6Max录像：开始录像停止处理流程");

        new Thread(() -> {
            try {
                // 步骤1: 检查当前录像状态
                if (!isA6MaxRecordProcessing) {
                    LogUtils.w(TAG, "A6Max当前未在录像状态");
                    LogUtils.file("A6Max录像：当前未在录像状态");
                    previewHandler.post(() -> {
                        MyProgressDialog.closeProgressDialog();
                        MyToast.show(activity, "当前未在录像状态");
                    });
                    return;
                }

                // 步骤2: 停止录像监控
                stopA6MaxRecordMonitor();

                // 步骤3: 执行A6Max特殊停止前处理
                if (!prepareA6MaxRecordStop()) {
                    LogUtils.e(TAG, "A6Max录像停止前处理失败");
                    LogUtils.file("A6Max录像：停止前处理失败");
                }

                // 步骤4: 停止录像
                final boolean ret = stopA6MaxRecordProcess();

                // 步骤5: 播放停止音效
                videoCaptureStartBeep.start();

                previewHandler.post(() -> {
                    MyProgressDialog.closeProgressDialog();
                    if (ret) {
                        LogUtils.i(TAG, "A6Max录像停止成功");
                        LogUtils.file("A6Max录像：录像停止成功");

                        // 更新状态
                        isA6MaxRecordProcessing = false;
                        curAppStateMode = PreviewMode.APP_STATE_VIDEO_PREVIEW;
                        stopVideoCaptureButtomChangeTimer();
                        stopRecordingLapseTimeTimer();

                        // 更新UI
                        previewView.setRemainRecordingTimeText(
                                ConvertTools.secondsToMinuteOrHours(cameraProperties.getRecordingRemainTime()));

                        // 执行A6Max录像后处理
                        handleA6MaxRecordPostProcess();

                    } else {
                        LogUtils.e(TAG, "A6Max录像停止失败");
                        LogUtils.file("A6Max录像：录像停止失败");
                        MyToast.show(activity, "A6Max录像停止失败");
                    }
                });

            } catch (Exception e) {
                LogUtils.e(TAG, "A6Max录像停止处理异常", e);
                LogUtils.file("A6Max录像：停止处理异常 - " + e.getMessage());
                previewHandler.post(() -> {
                    MyProgressDialog.closeProgressDialog();
                    isA6MaxRecordProcessing = false;
                    MyToast.show(activity, "A6Max录像停止处理异常");
                });
            }
        }).start();
    }

    /**
     * 准备A6Max录像停止前处理
     */
    private boolean prepareA6MaxRecordStop() {
        LogUtils.d(TAG, "准备A6Max录像停止前处理");
        LogUtils.file("A6Max录像：准备停止前处理");

        try {
            // A6Max特殊的停止前处理逻辑
            // 例如：保存当前录像状态、准备文件处理等

            LogUtils.d(TAG, "A6Max录像停止前处理完成");
            LogUtils.file("A6Max录像：停止前处理完成");
            return true;

        } catch (Exception e) {
            LogUtils.e(TAG, "A6Max录像停止前处理失败", e);
            LogUtils.file("A6Max录像：停止前处理失败 - " + e.getMessage());
            return false;
        }
    }

    /**
     * 执行A6Max录像停止处理
     */
    private boolean stopA6MaxRecordProcess() {
        LogUtils.d(TAG, "执行A6Max录像停止处理");
        LogUtils.file("A6Max录像：执行录像停止处理");

        try {
            // 调用底层停止录像接口
            boolean ret = cameraAction.stopVideoCapture();

            if (ret) {
                LogUtils.d(TAG, "A6Max底层录像停止成功");
                LogUtils.file("A6Max录像：底层录像停止成功");
                return true;
            } else {
                LogUtils.e(TAG, "A6Max底层录像停止失败");
                LogUtils.file("A6Max录像：底层录像停止失败");
                return false;
            }

        } catch (Exception e) {
            LogUtils.e(TAG, "A6Max录像停止处理异常", e);
            LogUtils.file("A6Max录像：停止处理异常 - " + e.getMessage());
            return false;
        }
    }

    /**
     * A6Max录像后处理
     */
    private void handleA6MaxRecordPostProcess() {
        LogUtils.d(TAG, "开始A6Max录像后处理");
        LogUtils.file("A6Max录像：开始录像后处理");

        new Thread(() -> {
            try {
                // 清理A6Max录像数据
                cleanupA6MaxRecordData();

                LogUtils.d(TAG, "A6Max录像后处理完成");
                LogUtils.file("A6Max录像：录像后处理完成");

            } catch (Exception e) {
                LogUtils.e(TAG, "A6Max录像后处理异常", e);
                LogUtils.file("A6Max录像：后处理异常 - " + e.getMessage());
            }
        }).start();
    }

    /**
     * 清理A6Max录像数据
     */
    private void cleanupA6MaxRecordData() {
        LogUtils.d(TAG, "清理A6Max录像数据");
        LogUtils.file("A6Max录像：清理录像数据");

        try {
            isA6MaxRecordProcessing = false;

            // 停止所有相关定时器
            stopA6MaxRecordMonitor();

            LogUtils.d(TAG, "A6Max录像数据清理完成");
            LogUtils.file("A6Max录像：数据清理完成");

        } catch (Exception e) {
            LogUtils.e(TAG, "A6Max录像数据清理异常", e);
            LogUtils.file("A6Max录像：数据清理异常 - " + e.getMessage());
        }
    }

    @Override
    public void setA6MaxVideoMode(boolean isLongVideo) {
        if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_CAPTURE
                || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE) {
            ToastUtils.showShort("正在录像中，无法切换模式");
            return;
        }

        MyProgressDialog.showProgressDialog(activity, R.string.action_processing);
        AppExecutors.runOnIoThread(() -> {
            int modeId = isLongVideo ? A6RotateMotorState.RECORD_MODE_LONG_VIDEO : A6RotateMotorState.RECORD_MODE_SHORT_VIDEO;
            cameraProperties.setPropertyValue(PropertyId.A6MAX_RECORD_MODE, modeId);
            LogUtils.file("设置A6Max 录像模式：0xD762 = " + modeId);
            refreshA6MaxRecordMode();
            previewHandler.post(MyProgressDialog::closeProgressDialog);
        });
    }

    @Override
    public String canChangeMode() {
        if (isA6MaxCamera) {
            if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW ||
                    curAppStateMode == APP_STATE_TIMELAPSE_VIDEO_PREVIEW ||
                    curAppStateMode == PreviewMode.APP_STATE_VIDEO_CAPTURE
                    || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE) {
                //需要等相机归位结束才能切换
                int recordStatus = getCurrentPropertyValue(PropertyId.A6MAX_RECORD_STATUS);
                LogUtils.file("A6Max 从录像切换到摄像判断：0xD763 = " + recordStatus);
                //相机正在归位中
                if (recordStatus == A6MaxRecordStatus.RECORDING_AND_MOVING) {
                    return "相机录像中，请稍候";
                } else if (recordStatus == A6MaxRecordStatus.RECORDING_STOPPED_REPOSITIONING) {
                    return "相机归位中，请稍候";
                }
            }
        }
        return null;
    }


}

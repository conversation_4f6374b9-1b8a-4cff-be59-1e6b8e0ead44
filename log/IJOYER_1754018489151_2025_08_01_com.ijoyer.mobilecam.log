************* Log Head ****************
Date of Log        : 2025_08_01
Rom Info           : RomInfo{name=huawei, version=14.2.0}
Device Manufacturer: HUAWEI
Device Model       : TAS-AL00
Android Version    : 12
Android SDK        : 31
App VersionName    : 2.8.4
App VersionCode    : 84
************* Log Head ****************

11:21:32.412 D/MainActivity [main, com.ijoyer.camera.activity.MainActivity.onCreate(MainActivity.java:11)]: MainActivity OnCreate()
11:21:34.922 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, Security type: -1, Supplicant state: COMPLETED, Wi-Fi standard: 4, RSSI: -17, Link speed: 65Mbps, Tx Link speed: 65Mbps, Max Supported Tx Link speed: 72Mbps, Rx Link speed: -1Mbps, Max Supported Rx Link speed: 72Mbps, Frequency: 2412MHz, Net ID: -1, Metered hint: false, score: 60, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1
11:21:34.923 D/MainActivity [main, com.ijoyer.camera.activity.MainActivity.goToNext(MainActivity.java:3)]: 进入：lastExitPreviewActivityTime=1754018494921
11:21:36.161 D/DefaultTaskExecutor [main, com.icatch.mobilecam.utils.executor.DefaultTaskExecutor.<init>(DefaultTaskExecutor.java:8)]: 线程池最大线程数：16
11:21:36.477  [main, .refreshBatteryLevel(PreviewPresenter.java:4)]: 刷新电池电量：66
11:21:36.795  [main, .refreshBatteryLevel(PreviewPresenter.java:4)]: 刷新电池电量：66
11:22:05.292  [main, .startOrStopCapture(PreviewPresenter.java:1)]: 拍摄：点击拍摄按钮
11:22:05.464  [main, .startPhotoCapture(PreviewPresenter.java:1)]: 拍摄：开始拍摄
11:22:05.538  [main, .cancelA6CaptureTimer(PreviewPresenter.java:2)]: 拍摄：中断计时器
11:22:05.541  [main, .startA6CaptureTimer(PreviewPresenter.java:6)]: 拍摄：A6 开启计时器
11:22:05.545  [Thread-34, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 0
11:22:05.695  [Thread-35, .lambda$startPhotoCapture$13(PreviewPresenter.java:3)]: 拍摄：循环询问相机是否可以拍照开始
11:22:08.545  [Thread-34, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
11:22:10.743  [Thread-35, .lambda$startPhotoCapture$13(PreviewPresenter.java:13)]: 拍摄：循环询问相机是否可以拍照结束,相机准备好了：motorState=2
11:22:13.854 D/PhotoCapture [Thread-35, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
11:22:13.873  [Thread-34, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
11:22:13.952  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
11:22:15.452  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
11:22:15.455  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=0
11:22:15.506  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：1
11:22:15.730  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
11:22:15.733  [Thread-41, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
11:22:15.838  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
11:22:16.874  [Thread-34, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
11:22:19.875  [Thread-34, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
11:22:20.284  [Thread-41, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=1
11:22:23.686 D/PhotoCapture [Thread-41, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
11:22:23.704  [Thread-34, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
11:22:23.764  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
11:22:25.035  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
11:22:25.038  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=1
11:22:25.077  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：2
11:22:25.206  [Thread-48, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
11:22:25.280  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
11:22:25.546  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
11:22:26.706  [Thread-34, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
11:22:29.707  [Thread-34, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
11:22:29.966  [Thread-48, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=2
11:22:33.220 D/PhotoCapture [Thread-48, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
11:22:33.237  [Thread-34, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
11:22:33.276  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
11:22:35.452  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
11:22:35.455  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=2
11:22:35.511  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
11:22:35.609  [Thread-54, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
11:22:35.611  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
11:22:35.658  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
11:22:36.238  [Thread-34, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
11:22:36.736  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：0
11:22:37.143  [main, .refreshBatteryLevel(PreviewPresenter.java:4)]: 刷新电池电量：33
11:22:37.146  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:107)]: 收到电池电量变化通知：1
11:22:39.238  [Thread-34, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
11:22:40.311  [Thread-54, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=3
11:22:43.467 D/PhotoCapture [Thread-54, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
11:22:43.790  [Thread-34, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
11:22:43.909  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
11:22:45.001  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
11:22:45.005  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=3
11:22:45.128  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
11:22:45.132  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:82)]: 拍摄：理论上上拍摄完毕（countTimes == 4）
11:22:45.134  [Thread-57, $PreviewHandler.lambda$handleMessage$0(PreviewPresenter.java:2)]: 拍摄：循环询问相机是否已复位开始
11:22:45.517  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
11:22:45.520  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
11:22:46.791  [Thread-34, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
11:22:48.599 D/SPKey [arch_disk_io_5, com.icatch.mobilecam.utils.SPKey.isPhoneQualifiedPerformance(SPKey.java:3)]: CPU 数量：8
11:22:48.602 D/SPKey [arch_disk_io_5, com.icatch.mobilecam.utils.SPKey.isPhoneQualifiedPerformance(SPKey.java:9)]: 总内存大小：7925583872
11:22:48.606 D/SPKey [arch_disk_io_5, com.icatch.mobilecam.utils.SPKey.isPhoneQualifiedPerformance(SPKey.java:10)]: 可用内存大小：1916661760
11:22:48.610 D/PbDownloadManager [arch_disk_io_5, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:5)]: 开始HDR，当前模式：多线程模式
11:22:48.613 D/PbDownloadManager [arch_disk_io_5, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:7)]: 开始HDR，多线程模式，线程池数量：2
11:22:49.792  [Thread-34, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
11:22:52.821  [Thread-34, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
11:22:52.968  [Thread-57, .cancelA6CaptureTimer(PreviewPresenter.java:2)]: 拍摄：中断计时器
11:22:52.976  [Thread-57, $PreviewHandler.lambda$handleMessage$0(PreviewPresenter.java:12)]: 拍摄：循环询问相机是否已复位结束：相机已复位
11:22:54.869  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：8
11:22:55.799  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：0
11:22:55.805  [main, .refreshBatteryLevel(PreviewPresenter.java:4)]: 刷新电池电量：0
11:22:55.808  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:107)]: 收到电池电量变化通知：2
11:22:56.422 D/PreviewActivity [main, com.icatch.mobilecam.ui.activity.PreviewActivity.onDestroy(PreviewActivity.java:4)]: PreviewActivity:正常销毁
11:22:56.771 E/PbDownloadManager [HdrProcessorThread-0, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.hdr(PbDownloadManager.java:34)]: 需要HDR的文件不存在或无法读取：/data/user/0/com.ijoyer.mobilecam/cache/20250801_112211_2C.JPG
11:22:56.777 E/PbDownloadManager [HdrProcessorThread-0, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.hdr(PbDownloadManager.java:34)]: 需要HDR的文件不存在或无法读取：/data/user/0/com.ijoyer.mobilecam/cache/20250801_112211_3C.JPG
11:22:56.822 D/PbDownloadManager [arch_disk_io_5, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:27)]: HDR 结束：hdrList.size=2
11:23:02.291 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, Security type: 2, Supplicant state: COMPLETED, Wi-Fi standard: 5, RSSI: -35, Link speed: 1560Mbps, Tx Link speed: 1560Mbps, Max Supported Tx Link speed: 1733Mbps, Rx Link speed: 1733Mbps, Max Supported Rx Link speed: 1733Mbps, Frequency: 5240MHz, Net ID: -1, Metered hint: false, score: 60, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1
11:23:02.296 D/MainActivity [main, com.ijoyer.camera.activity.MainActivity.goToNext(MainActivity.java:3)]: 进入：lastExitPreviewActivityTime=6516
13:37:04.712 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, Security type: 2, Supplicant state: COMPLETED, Wi-Fi standard: 5, RSSI: -31, Link speed: 1560Mbps, Tx Link speed: 1560Mbps, Max Supported Tx Link speed: 1733Mbps, Rx Link speed: 1560Mbps, Max Supported Rx Link speed: 1733Mbps, Frequency: 5240MHz, Net ID: -1, Metered hint: false, score: 60, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1
13:37:06.540 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, Security type: 2, Supplicant state: COMPLETED, Wi-Fi standard: 5, RSSI: -31, Link speed: 1560Mbps, Tx Link speed: 1560Mbps, Max Supported Tx Link speed: 1733Mbps, Rx Link speed: 1560Mbps, Max Supported Rx Link speed: 1733Mbps, Frequency: 5240MHz, Net ID: -1, Metered hint: false, score: 60, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1
13:38:03.115 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, Security type: 2, Supplicant state: COMPLETED, Wi-Fi standard: 5, RSSI: -31, Link speed: 1560Mbps, Tx Link speed: 1560Mbps, Max Supported Tx Link speed: 1733Mbps, Rx Link speed: 1560Mbps, Max Supported Rx Link speed: 1733Mbps, Frequency: 5240MHz, Net ID: -1, Metered hint: false, score: 60, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1
13:38:08.702 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, Security type: 2, Supplicant state: COMPLETED, Wi-Fi standard: 5, RSSI: -31, Link speed: 1560Mbps, Tx Link speed: 1560Mbps, Max Supported Tx Link speed: 1733Mbps, Rx Link speed: 1560Mbps, Max Supported Rx Link speed: 1733Mbps, Frequency: 5240MHz, Net ID: -1, Metered hint: false, score: 60, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1
13:40:13.329 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, Security type: 2, Supplicant state: COMPLETED, Wi-Fi standard: 5, RSSI: -31, Link speed: 1560Mbps, Tx Link speed: 1560Mbps, Max Supported Tx Link speed: 1733Mbps, Rx Link speed: 1560Mbps, Max Supported Rx Link speed: 1733Mbps, Frequency: 5240MHz, Net ID: -1, Metered hint: false, score: 60, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1
13:40:43.024 D/PanoramaPlayerActivity [main, com.ijoyer.camera.activity.PanoramaPlayerActivity$7.onSuccess(PanoramaPlayerActivity.java:4)]: PanoramaPlayerActivity HMS Image Vision API 初始化成功
13:40:43.026 D/PanoramaPlayerActivity [main, com.ijoyer.camera.activity.PanoramaPlayerActivity$7.onSuccess(PanoramaPlayerActivity.java:4)]: PanoramaPlayerActivity HMS Image Vision API 初始化成功
14:58:27.132 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, Security type: -1, Supplicant state: COMPLETED, Wi-Fi standard: 4, RSSI: -22, Link speed: 65Mbps, Tx Link speed: 65Mbps, Max Supported Tx Link speed: 72Mbps, Rx Link speed: -1Mbps, Max Supported Rx Link speed: 72Mbps, Frequency: 2437MHz, Net ID: -1, Metered hint: false, score: 60, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1
14:58:27.140 D/MainActivity [main, com.ijoyer.camera.activity.MainActivity.goToNext(MainActivity.java:3)]: 进入：lastExitPreviewActivityTime=12931358
14:58:28.733  [main, .refreshBatteryLevel(PreviewPresenter.java:4)]: 刷新电池电量：100
14:58:29.051  [main, .refreshBatteryLevel(PreviewPresenter.java:4)]: 刷新电池电量：100
14:58:34.343  [main, .refreshBatteryLevel(PreviewPresenter.java:4)]: 刷新电池电量：100
14:58:34.666  [main, .refreshBatteryLevel(PreviewPresenter.java:4)]: 刷新电池电量：100
14:58:38.807 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, Security type: -1, Supplicant state: COMPLETED, Wi-Fi standard: 4, RSSI: -22, Link speed: 65Mbps, Tx Link speed: 65Mbps, Max Supported Tx Link speed: 72Mbps, Rx Link speed: -1Mbps, Max Supported Rx Link speed: 72Mbps, Frequency: 2437MHz, Net ID: -1, Metered hint: false, score: 60, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1
14:58:47.282  [main, .refreshBatteryLevel(PreviewPresenter.java:4)]: 刷新电池电量：100
14:58:47.613  [main, .refreshBatteryLevel(PreviewPresenter.java:4)]: 刷新电池电量：100
14:58:59.832  [main, .startOrStopCapture(PreviewPresenter.java:1)]: 拍摄：点击拍摄按钮
14:59:00.001  [main, .startPhotoCapture(PreviewPresenter.java:1)]: 拍摄：开始拍摄
14:59:00.076  [main, .cancelA6CaptureTimer(PreviewPresenter.java:2)]: 拍摄：中断计时器
14:59:00.078  [main, .startA6CaptureTimer(PreviewPresenter.java:6)]: 拍摄：A6 开启计时器
14:59:00.083  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 0
14:59:00.215  [Thread-120, .lambda$startPhotoCapture$13(PreviewPresenter.java:3)]: 拍摄：循环询问相机是否可以拍照开始
14:59:03.082  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
14:59:05.326  [Thread-120, .lambda$startPhotoCapture$13(PreviewPresenter.java:13)]: 拍摄：循环询问相机是否可以拍照结束,相机准备好了：motorState=2
14:59:08.706 D/PhotoCapture [Thread-120, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
14:59:08.739  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
14:59:08.803  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
14:59:10.185  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
14:59:10.192  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=0
14:59:10.249  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：1
14:59:10.453  [Thread-125, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
14:59:10.458  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
14:59:10.572  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
14:59:11.740  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
14:59:14.741  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
14:59:15.069  [Thread-125, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=1
14:59:18.496 D/PhotoCapture [Thread-125, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
14:59:18.525  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
14:59:18.695  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
14:59:19.677  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
14:59:19.688  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=1
14:59:19.723  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：2
14:59:19.902  [Thread-129, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
14:59:19.910  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
14:59:20.139  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
14:59:21.528  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
14:59:24.529  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
14:59:24.542  [Thread-129, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=2
14:59:28.026 D/PhotoCapture [Thread-129, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
14:59:28.044  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
14:59:28.120  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
14:59:29.479  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
14:59:29.485  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=2
14:59:29.519  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
14:59:29.824  [Thread-133, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
14:59:29.830  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
14:59:29.837  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
14:59:31.045  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
14:59:34.046  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
14:59:34.319  [Thread-133, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=3
14:59:37.444 D/PhotoCapture [Thread-133, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
14:59:37.464  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
14:59:37.608  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
14:59:37.692  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
14:59:38.994  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
14:59:39.002  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=3
14:59:39.025  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
14:59:39.038  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:82)]: 拍摄：理论上上拍摄完毕（countTimes == 4）
14:59:39.048  [Thread-136, $PreviewHandler.lambda$handleMessage$0(PreviewPresenter.java:2)]: 拍摄：循环询问相机是否已复位开始
14:59:39.288  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
14:59:40.466  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
14:59:42.280 D/PbDownloadManager [arch_disk_io_2, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:5)]: 开始HDR，当前模式：多线程模式
14:59:42.290 D/PbDownloadManager [arch_disk_io_2, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:7)]: 开始HDR，多线程模式，线程池数量：2
14:59:43.467  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
14:59:46.552  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
14:59:46.940  [Thread-136, .cancelA6CaptureTimer(PreviewPresenter.java:2)]: 拍摄：中断计时器
14:59:46.944  [Thread-136, $PreviewHandler.lambda$handleMessage$0(PreviewPresenter.java:12)]: 拍摄：循环询问相机是否已复位结束：相机已复位
14:59:56.936 D/PbDownloadManager [arch_disk_io_2, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:27)]: HDR 结束：hdrList.size=4
14:59:57.113 D/StitchUtils [arch_disk_io_2, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:10)]: 从标定文件中获取到的标定参数：
14:59:57.119 D/StitchUtils [arch_disk_io_2, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:15)]: 获取临时标定参数失败：length=0; index=5
14:59:57.137 D/StitchUtils [arch_disk_io_2, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:48)]: 开始拼接，拼接参数={"calistr":"QnypDMewBPCJYTFZdJB5DbXyDbvGN1K6CiEFKbrVMoxHY\/R5DA0GKAe5YIAhHUogrWiQBN0tqR20hWrrR+NSC4Sx3mtU1O+5xWKkAHe0qH767tf5AsdTNkGv5WC7TAkWfQhDCUoSxXcye3xECq3Sm43k+nIb8cjLcRXEEuXj\/eSIxbZFZWoz+kTbzUaOfxeizTIr\/qfqVA5136gvq0rUah2gtsLNV7\/iuwraDjMzR5XIY4UOwyPVjywkH5XHtZbdVPD5Is2UjUjqct5yruYzKN5jcylCnWq+lwRKExfyri5jwJV2VYHJUA1mhaFz7pvJAThH1T0U2PllDyLY9NXcdTD7KkYIcyvV1oxDG3e5zI9LWYfFpTe2TrEgQMcuTPUeKVOY6fKiF79mCluQZ1PjPVuqvT3K4L4rtc7DOTnw58SH3+vcC6XeaWgxej1aIh9eg\/WJZllOnkIv+jOL8TTi2TuoJPJbGOscZUGunwQ2dAT26n5c1UJVAMaZNRnNldpRfWOK1S\/rw2e9F054ptiA\/Y\/1xGWDKmFKS+4bVhafZhcB8oO8PuXbO3Ur0xRYlVwiHDTSwpEm5T3RkS0MNUo9qGUruf5ae3vHgYK08+N3AuSumjvUBzr7\/JqET7+mDQQAHyQPo3Wu8PV4JmNTSmbF\/ZpktEEJfItSd1aPS+029qggSjdI8yw2\/jTXvtgVk3iWJXak2UFfVXteyKMb5zyO4hvw1ePCZ81Z1TPEik\/YJQ\/UBloBt+UHf9vpFocHco+0UiOYokhAYfFRAi9BZ17z7CtrhrpexratTPCaxpDz9j5m8Uk9EPRUJFferbFBPLlv2bIghEpCn9clj4dzJzKuSwR0Hilx8Q4cg45W6D6EbsC2hxP3FvRK\/W+Ft7jxIrY\/Qv6EiIuOPf\/C6miLcGobOCHkxbW7\/DO0VCsfdDI9dysYQtdhIG8iM2cBUaWqYnkCM5\/B1jVQMnjlvaVvR75\/BRG9WAfBZXjpLb+KNTvcQ6Nud2KckMT0R\/JLspQ0opScQZW5o4LkB5HkTP4HPU6RUY6lCaa7QfUgxAAckBTHb7slu4Gpb7GbRlWb1AOtPeBbJeMZqvt6jvlE+T\/+rFI0CksCcZ5YMGodbGWlVW4NiMTjAAiSffRKO\/21UyoGyCabPenmTX+x56KxAKey22Ym4SDjvNW+irL40E0AX8y2JqguDK+nuLMBUFx9ADXgrfYnQdsZxa2R8U8Pr5XvkRlSAjaCd49zfD7Y+wgFQQi9hKQ++ETkrMuH9zDAhE5lsCDFA8QViYgq49jtCeZmz2VP1gjjdvGgTeUrXdsvKehI6QJsnewBEabFeshg2c4SSK6zh53Sun9y3zt7bn\/pR49ez1\/Qp5JIOMmzNRMmcz5XIFF5Cqaftcd7DRdYIJGEsCgNVXjX0QyH4Q1sPZO00YTkq43mf\/\/nLA1f+YxNLvaMauYQJ0NqO0Mt2bmK19Hifh2iYhWVVM7LunkrjY15dIvZV2kJljMcNVlJUinSjtEApAkhB6e1cFX1C9tRHBrwFW7SN6sXMpH\/WAeVQOYDUC2jxlSc0\/+LIaA2aa0Vymva+fECDPhRxWtWTLFdo3Rk2ycbb6ygmwBlEvXiehDMcxiFYV10b+\/1oo6B098xZiJ2XJSaCa+FQRE9UsydvRHrJWRUF10YLnZfyU8pgy\/mQ6YUJVWYzneu\/WN2mwD9kQLIWHc=","accesskey":"ZQnAbZjCvNCjXY9rFSHtBct1XfkvOv5d6w9FLD5WPyddiDE584MbKL1na5X0Nao22bnsFRXu6bC8ZGO\/GJcaKw==","cachefile":"\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/","img":["\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_145906_69648066654996_566.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_145906_69647814403434_567.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_145906_69654790472183_567.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_145906_69654890797704_566.jpg"],"dstwidth":8000,"dstheight":4000,"isopt":0,"ismulti":1,"mutiluv":1}
15:00:07.353 D/StitchUtils [arch_disk_io_2, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:50)]: 拼接结束，拼接结果=0
15:00:59.622  [main, .startOrStopCapture(PreviewPresenter.java:1)]: 拍摄：点击拍摄按钮
15:00:59.828  [main, .startPhotoCapture(PreviewPresenter.java:1)]: 拍摄：开始拍摄
15:00:59.999  [main, .cancelA6CaptureTimer(PreviewPresenter.java:2)]: 拍摄：中断计时器
15:01:00.002  [main, .startA6CaptureTimer(PreviewPresenter.java:6)]: 拍摄：A6 开启计时器
15:01:00.212  [Thread-138, .lambda$startPhotoCapture$13(PreviewPresenter.java:3)]: 拍摄：循环询问相机是否可以拍照开始
15:01:00.223  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:01:03.224  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:01:05.319  [Thread-138, .lambda$startPhotoCapture$13(PreviewPresenter.java:13)]: 拍摄：循环询问相机是否可以拍照结束,相机准备好了：motorState=2
15:01:08.892 D/PhotoCapture [Thread-138, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:01:09.087  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:01:09.122  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:01:09.356  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:01:09.883  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:01:09.891  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=0
15:01:09.942  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：1
15:01:10.256  [Thread-143, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:01:10.265  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:01:12.088  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:01:14.802  [Thread-143, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=1
15:01:17.819 D/PhotoCapture [Thread-143, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:01:17.841  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:01:18.142  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:01:20.301  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:01:20.308  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=1
15:01:20.326  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：2
15:01:20.372  [Thread-147, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:01:20.424  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:01:20.591  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:01:20.842  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:01:23.845  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:01:25.265  [Thread-147, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=2
15:01:28.444 D/PhotoCapture [Thread-147, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:01:28.456  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:01:28.498  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:01:30.490  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:01:30.496  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=2
15:01:30.521  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
15:01:30.656  [Thread-151, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:01:30.663  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:01:30.671  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:01:31.460  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:01:34.460  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:01:35.432  [Thread-151, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=3
15:01:38.686 D/PhotoCapture [Thread-151, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:01:38.699  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:01:38.764  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:01:40.055  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:01:40.059  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=3
15:01:40.090  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
15:01:40.100  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:82)]: 拍摄：理论上上拍摄完毕（countTimes == 4）
15:01:40.105  [Thread-154, $PreviewHandler.lambda$handleMessage$0(PreviewPresenter.java:2)]: 拍摄：循环询问相机是否已复位开始
15:01:40.506  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:01:40.514  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:01:41.701  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:01:43.054 D/PbDownloadManager [arch_disk_io_7, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:5)]: 开始HDR，当前模式：多线程模式
15:01:43.062 D/PbDownloadManager [arch_disk_io_7, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:7)]: 开始HDR，多线程模式，线程池数量：2
15:01:44.702  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:01:47.703  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:01:47.910  [Thread-154, .cancelA6CaptureTimer(PreviewPresenter.java:2)]: 拍摄：中断计时器
15:01:47.915  [Thread-154, $PreviewHandler.lambda$handleMessage$0(PreviewPresenter.java:12)]: 拍摄：循环询问相机是否已复位结束：相机已复位
15:01:56.019 D/PbDownloadManager [arch_disk_io_7, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:27)]: HDR 结束：hdrList.size=4
15:01:56.172 D/StitchUtils [arch_disk_io_7, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:10)]: 从标定文件中获取到的标定参数：
15:01:56.188 D/StitchUtils [arch_disk_io_7, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:15)]: 获取临时标定参数失败：length=0; index=5
15:01:56.195 D/StitchUtils [arch_disk_io_7, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:48)]: 开始拼接，拼接参数={"calistr":"QnypDMewBPCJYTFZdJB5DbXyDbvGN1K6CiEFKbrVMoxHY\/R5DA0GKAe5YIAhHUogrWiQBN0tqR20hWrrR+NSC4Sx3mtU1O+5xWKkAHe0qH767tf5AsdTNkGv5WC7TAkWfQhDCUoSxXcye3xECq3Sm43k+nIb8cjLcRXEEuXj\/eSIxbZFZWoz+kTbzUaOfxeizTIr\/qfqVA5136gvq0rUah2gtsLNV7\/iuwraDjMzR5XIY4UOwyPVjywkH5XHtZbdVPD5Is2UjUjqct5yruYzKN5jcylCnWq+lwRKExfyri5jwJV2VYHJUA1mhaFz7pvJAThH1T0U2PllDyLY9NXcdTD7KkYIcyvV1oxDG3e5zI9LWYfFpTe2TrEgQMcuTPUeKVOY6fKiF79mCluQZ1PjPVuqvT3K4L4rtc7DOTnw58SH3+vcC6XeaWgxej1aIh9eg\/WJZllOnkIv+jOL8TTi2TuoJPJbGOscZUGunwQ2dAT26n5c1UJVAMaZNRnNldpRfWOK1S\/rw2e9F054ptiA\/Y\/1xGWDKmFKS+4bVhafZhcB8oO8PuXbO3Ur0xRYlVwiHDTSwpEm5T3RkS0MNUo9qGUruf5ae3vHgYK08+N3AuSumjvUBzr7\/JqET7+mDQQAHyQPo3Wu8PV4JmNTSmbF\/ZpktEEJfItSd1aPS+029qggSjdI8yw2\/jTXvtgVk3iWJXak2UFfVXteyKMb5zyO4hvw1ePCZ81Z1TPEik\/YJQ\/UBloBt+UHf9vpFocHco+0UiOYokhAYfFRAi9BZ17z7CtrhrpexratTPCaxpDz9j5m8Uk9EPRUJFferbFBPLlv2bIghEpCn9clj4dzJzKuSwR0Hilx8Q4cg45W6D6EbsC2hxP3FvRK\/W+Ft7jxIrY\/Qv6EiIuOPf\/C6miLcGobOCHkxbW7\/DO0VCsfdDI9dysYQtdhIG8iM2cBUaWqYnkCM5\/B1jVQMnjlvaVvR75\/BRG9WAfBZXjpLb+KNTvcQ6Nud2KckMT0R\/JLspQ0opScQZW5o4LkB5HkTP4HPU6RUY6lCaa7QfUgxAAckBTHb7slu4Gpb7GbRlWb1AOtPeBbJeMZqvt6jvlE+T\/+rFI0CksCcZ5YMGodbGWlVW4NiMTjAAiSffRKO\/21UyoGyCabPenmTX+x56KxAKey22Ym4SDjvNW+irL40E0AX8y2JqguDK+nuLMBUFx9ADXgrfYnQdsZxa2R8U8Pr5XvkRlSAjaCd49zfD7Y+wgFQQi9hKQ++ETkrMuH9zDAhE5lsCDFA8QViYgq49jtCeZmz2VP1gjjdvGgTeUrXdsvKehI6QJsnewBEabFeshg2c4SSK6zh53Sun9y3zt7bn\/pR49ez1\/Qp5JIOMmzNRMmcz5XIFF5Cqaftcd7DRdYIJGEsCgNVXjX0QyH4Q1sPZO00YTkq43mf\/\/nLA1f+YxNLvaMauYQJ0NqO0Mt2bmK19Hifh2iYhWVVM7LunkrjY15dIvZV2kJljMcNVlJUinSjtEApAkhB6e1cFX1C9tRHBrwFW7SN6sXMpH\/WAeVQOYDUC2jxlSc0\/+LIaA2aa0Vymva+fECDPhRxWtWTLFdo3Rk2ycbb6ygmwBlEvXiehDMcxiFYV10b+\/1oo6B098xZiJ2XJSaCa+FQRE9UsydvRHrJWRUF10YLnZfyU8pgy\/mQ6YUJVWYzneu\/WN2mwD9kQLIWHc=","accesskey":"ZQnAbZjCvNCjXY9rFSHtBct1XfkvOv5d6w9FLD5WPyddiDE584MbKL1na5X0Nao22bnsFRXu6bC8ZGO\/GJcaKw==","cachefile":"\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/","img":["\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150106_69767645027374_598.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150106_69767733616436_599.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150106_69773851587269_598.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150106_69773989651331_599.jpg"],"dstwidth":8000,"dstheight":4000,"isopt":0,"ismulti":1,"mutiluv":1}
15:02:06.615 D/StitchUtils [arch_disk_io_7, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:50)]: 拼接结束，拼接结果=0
15:02:36.814  [main, .startOrStopCapture(PreviewPresenter.java:1)]: 拍摄：点击拍摄按钮
15:02:36.986  [main, .startPhotoCapture(PreviewPresenter.java:1)]: 拍摄：开始拍摄
15:02:37.057  [main, .cancelA6CaptureTimer(PreviewPresenter.java:2)]: 拍摄：中断计时器
15:02:37.060  [main, .startA6CaptureTimer(PreviewPresenter.java:6)]: 拍摄：A6 开启计时器
15:02:37.160  [Thread-156, .lambda$startPhotoCapture$13(PreviewPresenter.java:3)]: 拍摄：循环询问相机是否可以拍照开始
15:02:37.165  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:02:40.167  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:02:42.241  [Thread-156, .lambda$startPhotoCapture$13(PreviewPresenter.java:13)]: 拍摄：循环询问相机是否可以拍照结束,相机准备好了：motorState=2
15:02:46.578 D/PhotoCapture [Thread-156, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:02:46.590  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:02:46.673  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:02:47.029  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:02:47.036  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=0
15:02:47.069  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：1
15:02:47.269  [Thread-161, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:02:47.278  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:02:47.416  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:02:49.592  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:02:52.022  [Thread-161, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=1
15:02:55.001 D/PhotoCapture [Thread-161, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:02:55.018  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:02:55.078  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:02:57.104  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:02:57.110  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=1
15:02:57.124  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：2
15:02:57.258  [Thread-165, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:02:57.265  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:02:57.274  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:02:58.019  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:03:01.020  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:03:02.042  [Thread-165, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=2
15:03:03.200 D/PhotoCapture [Thread-165, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:03:04.122  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:03:04.346  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:03:05.436  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:03:06.671  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:03:06.678  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=2
15:03:06.720  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
15:03:06.872  [Thread-169, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:03:06.881  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:03:07.125  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:03:10.126  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:03:11.632  [Thread-169, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=3
15:03:15.044 D/PhotoCapture [Thread-169, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:03:15.075  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:03:15.113  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:03:16.377  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:03:16.389  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=3
15:03:16.471  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
15:03:16.487  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:82)]: 拍摄：理论上上拍摄完毕（countTimes == 4）
15:03:16.501  [Thread-172, $PreviewHandler.lambda$handleMessage$0(PreviewPresenter.java:2)]: 拍摄：循环询问相机是否已复位开始
15:03:16.867  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:03:16.876  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:03:18.077  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:03:20.693 D/PbDownloadManager [arch_disk_io_2, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:5)]: 开始HDR，当前模式：多线程模式
15:03:20.698 D/PbDownloadManager [arch_disk_io_2, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:7)]: 开始HDR，多线程模式，线程池数量：2
15:03:21.078  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:03:24.080  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:03:24.328  [Thread-172, .cancelA6CaptureTimer(PreviewPresenter.java:2)]: 拍摄：中断计时器
15:03:24.334  [Thread-172, $PreviewHandler.lambda$handleMessage$0(PreviewPresenter.java:12)]: 拍摄：循环询问相机是否已复位结束：相机已复位
15:03:34.234 D/PbDownloadManager [arch_disk_io_2, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:27)]: HDR 结束：hdrList.size=4
15:03:34.404 D/StitchUtils [arch_disk_io_2, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:10)]: 从标定文件中获取到的标定参数：
15:03:34.410 D/StitchUtils [arch_disk_io_2, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:15)]: 获取临时标定参数失败：length=0; index=5
15:03:34.416 D/StitchUtils [arch_disk_io_2, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:48)]: 开始拼接，拼接参数={"calistr":"QnypDMewBPCJYTFZdJB5DbXyDbvGN1K6CiEFKbrVMoxHY\/R5DA0GKAe5YIAhHUogrWiQBN0tqR20hWrrR+NSC4Sx3mtU1O+5xWKkAHe0qH767tf5AsdTNkGv5WC7TAkWfQhDCUoSxXcye3xECq3Sm43k+nIb8cjLcRXEEuXj\/eSIxbZFZWoz+kTbzUaOfxeizTIr\/qfqVA5136gvq0rUah2gtsLNV7\/iuwraDjMzR5XIY4UOwyPVjywkH5XHtZbdVPD5Is2UjUjqct5yruYzKN5jcylCnWq+lwRKExfyri5jwJV2VYHJUA1mhaFz7pvJAThH1T0U2PllDyLY9NXcdTD7KkYIcyvV1oxDG3e5zI9LWYfFpTe2TrEgQMcuTPUeKVOY6fKiF79mCluQZ1PjPVuqvT3K4L4rtc7DOTnw58SH3+vcC6XeaWgxej1aIh9eg\/WJZllOnkIv+jOL8TTi2TuoJPJbGOscZUGunwQ2dAT26n5c1UJVAMaZNRnNldpRfWOK1S\/rw2e9F054ptiA\/Y\/1xGWDKmFKS+4bVhafZhcB8oO8PuXbO3Ur0xRYlVwiHDTSwpEm5T3RkS0MNUo9qGUruf5ae3vHgYK08+N3AuSumjvUBzr7\/JqET7+mDQQAHyQPo3Wu8PV4JmNTSmbF\/ZpktEEJfItSd1aPS+029qggSjdI8yw2\/jTXvtgVk3iWJXak2UFfVXteyKMb5zyO4hvw1ePCZ81Z1TPEik\/YJQ\/UBloBt+UHf9vpFocHco+0UiOYokhAYfFRAi9BZ17z7CtrhrpexratTPCaxpDz9j5m8Uk9EPRUJFferbFBPLlv2bIghEpCn9clj4dzJzKuSwR0Hilx8Q4cg45W6D6EbsC2hxP3FvRK\/W+Ft7jxIrY\/Qv6EiIuOPf\/C6miLcGobOCHkxbW7\/DO0VCsfdDI9dysYQtdhIG8iM2cBUaWqYnkCM5\/B1jVQMnjlvaVvR75\/BRG9WAfBZXjpLb+KNTvcQ6Nud2KckMT0R\/JLspQ0opScQZW5o4LkB5HkTP4HPU6RUY6lCaa7QfUgxAAckBTHb7slu4Gpb7GbRlWb1AOtPeBbJeMZqvt6jvlE+T\/+rFI0CksCcZ5YMGodbGWlVW4NiMTjAAiSffRKO\/21UyoGyCabPenmTX+x56KxAKey22Ym4SDjvNW+irL40E0AX8y2JqguDK+nuLMBUFx9ADXgrfYnQdsZxa2R8U8Pr5XvkRlSAjaCd49zfD7Y+wgFQQi9hKQ++ETkrMuH9zDAhE5lsCDFA8QViYgq49jtCeZmz2VP1gjjdvGgTeUrXdsvKehI6QJsnewBEabFeshg2c4SSK6zh53Sun9y3zt7bn\/pR49ez1\/Qp5JIOMmzNRMmcz5XIFF5Cqaftcd7DRdYIJGEsCgNVXjX0QyH4Q1sPZO00YTkq43mf\/\/nLA1f+YxNLvaMauYQJ0NqO0Mt2bmK19Hifh2iYhWVVM7LunkrjY15dIvZV2kJljMcNVlJUinSjtEApAkhB6e1cFX1C9tRHBrwFW7SN6sXMpH\/WAeVQOYDUC2jxlSc0\/+LIaA2aa0Vymva+fECDPhRxWtWTLFdo3Rk2ycbb6ygmwBlEvXiehDMcxiFYV10b+\/1oo6B098xZiJ2XJSaCa+FQRE9UsydvRHrJWRUF10YLnZfyU8pgy\/mQ6YUJVWYzneu\/WN2mwD9kQLIWHc=","accesskey":"ZQnAbZjCvNCjXY9rFSHtBct1XfkvOv5d6w9FLD5WPyddiDE584MbKL1na5X0Nao22bnsFRXu6bC8ZGO\/GJcaKw==","cachefile":"\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/","img":["\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150243_69865555178401_630.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150243_69865425159651_631.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150243_69872199236733_631.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150243_69871892132566_630.jpg"],"dstwidth":8000,"dstheight":4000,"isopt":0,"ismulti":1,"mutiluv":1}
15:03:44.840 D/StitchUtils [arch_disk_io_2, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:50)]: 拼接结束，拼接结果=0
15:04:28.764  [main, .startOrStopCapture(PreviewPresenter.java:1)]: 拍摄：点击拍摄按钮
15:04:28.951  [main, .startPhotoCapture(PreviewPresenter.java:1)]: 拍摄：开始拍摄
15:04:29.020  [main, .cancelA6CaptureTimer(PreviewPresenter.java:2)]: 拍摄：中断计时器
15:04:29.022  [main, .startA6CaptureTimer(PreviewPresenter.java:6)]: 拍摄：A6 开启计时器
15:04:29.027  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 0
15:04:29.093  [Thread-174, .lambda$startPhotoCapture$13(PreviewPresenter.java:3)]: 拍摄：循环询问相机是否可以拍照开始
15:04:32.028  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:04:34.160  [Thread-174, .lambda$startPhotoCapture$13(PreviewPresenter.java:13)]: 拍摄：循环询问相机是否可以拍照结束,相机准备好了：motorState=2
15:04:40.971 D/PhotoCapture [Thread-174, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:04:40.979  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:04:41.067  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:04:41.073  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:04:41.078  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=0
15:04:41.101  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：1
15:04:41.227  [Thread-179, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:04:41.235  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:04:41.403  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:04:43.642  [Thread-179, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=1
15:04:44.998 D/PhotoCapture [Thread-179, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:04:45.054  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:04:46.651  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:04:48.098  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:04:48.668  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:04:48.675  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=1
15:04:48.826  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：2
15:04:49.086  [Thread-183, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:04:49.095  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:04:49.157  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:04:51.099  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:04:53.737  [Thread-183, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=2
15:04:56.956 D/PhotoCapture [Thread-183, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:04:57.126  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:04:57.177  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:04:58.479  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:04:58.488  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=2
15:04:58.516  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
15:04:58.788  [Thread-187, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:04:58.797  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:04:58.893  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:05:00.127  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:05:03.129  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:05:03.405  [Thread-187, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=3
15:05:10.363 D/PhotoCapture [Thread-187, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:05:10.376  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:05:10.431  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:05:10.451  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:05:10.456  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=3
15:05:10.474  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
15:05:10.482  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:82)]: 拍摄：理论上上拍摄完毕（countTimes == 4）
15:05:10.489  [Thread-190, $PreviewHandler.lambda$handleMessage$0(PreviewPresenter.java:2)]: 拍摄：循环询问相机是否已复位开始
15:05:10.852  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:05:10.867  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:05:13.377  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:05:15.206 D/PbDownloadManager [arch_disk_io_1, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:5)]: 开始HDR，当前模式：多线程模式
15:05:15.214 D/PbDownloadManager [arch_disk_io_1, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:7)]: 开始HDR，多线程模式，线程池数量：2
15:05:16.202  [Thread-190, .cancelA6CaptureTimer(PreviewPresenter.java:2)]: 拍摄：中断计时器
15:05:16.207  [Thread-190, $PreviewHandler.lambda$handleMessage$0(PreviewPresenter.java:12)]: 拍摄：循环询问相机是否已复位结束：相机已复位
15:05:28.797  [main, .startOrStopCapture(PreviewPresenter.java:1)]: 拍摄：点击拍摄按钮
15:05:28.985  [main, .startPhotoCapture(PreviewPresenter.java:1)]: 拍摄：开始拍摄
15:05:29.282  [main, .cancelA6CaptureTimer(PreviewPresenter.java:2)]: 拍摄：中断计时器
15:05:29.284  [main, .startA6CaptureTimer(PreviewPresenter.java:6)]: 拍摄：A6 开启计时器
15:05:29.310 D/PbDownloadManager [arch_disk_io_1, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:27)]: HDR 结束：hdrList.size=4
15:05:29.431 D/StitchUtils [arch_disk_io_1, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:10)]: 从标定文件中获取到的标定参数：
15:05:29.433 D/StitchUtils [arch_disk_io_1, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:15)]: 获取临时标定参数失败：length=0; index=5
15:05:29.434 D/StitchUtils [arch_disk_io_1, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:48)]: 开始拼接，拼接参数={"calistr":"QnypDMewBPCJYTFZdJB5DbXyDbvGN1K6CiEFKbrVMoxHY\/R5DA0GKAe5YIAhHUogrWiQBN0tqR20hWrrR+NSC4Sx3mtU1O+5xWKkAHe0qH767tf5AsdTNkGv5WC7TAkWfQhDCUoSxXcye3xECq3Sm43k+nIb8cjLcRXEEuXj\/eSIxbZFZWoz+kTbzUaOfxeizTIr\/qfqVA5136gvq0rUah2gtsLNV7\/iuwraDjMzR5XIY4UOwyPVjywkH5XHtZbdVPD5Is2UjUjqct5yruYzKN5jcylCnWq+lwRKExfyri5jwJV2VYHJUA1mhaFz7pvJAThH1T0U2PllDyLY9NXcdTD7KkYIcyvV1oxDG3e5zI9LWYfFpTe2TrEgQMcuTPUeKVOY6fKiF79mCluQZ1PjPVuqvT3K4L4rtc7DOTnw58SH3+vcC6XeaWgxej1aIh9eg\/WJZllOnkIv+jOL8TTi2TuoJPJbGOscZUGunwQ2dAT26n5c1UJVAMaZNRnNldpRfWOK1S\/rw2e9F054ptiA\/Y\/1xGWDKmFKS+4bVhafZhcB8oO8PuXbO3Ur0xRYlVwiHDTSwpEm5T3RkS0MNUo9qGUruf5ae3vHgYK08+N3AuSumjvUBzr7\/JqET7+mDQQAHyQPo3Wu8PV4JmNTSmbF\/ZpktEEJfItSd1aPS+029qggSjdI8yw2\/jTXvtgVk3iWJXak2UFfVXteyKMb5zyO4hvw1ePCZ81Z1TPEik\/YJQ\/UBloBt+UHf9vpFocHco+0UiOYokhAYfFRAi9BZ17z7CtrhrpexratTPCaxpDz9j5m8Uk9EPRUJFferbFBPLlv2bIghEpCn9clj4dzJzKuSwR0Hilx8Q4cg45W6D6EbsC2hxP3FvRK\/W+Ft7jxIrY\/Qv6EiIuOPf\/C6miLcGobOCHkxbW7\/DO0VCsfdDI9dysYQtdhIG8iM2cBUaWqYnkCM5\/B1jVQMnjlvaVvR75\/BRG9WAfBZXjpLb+KNTvcQ6Nud2KckMT0R\/JLspQ0opScQZW5o4LkB5HkTP4HPU6RUY6lCaa7QfUgxAAckBTHb7slu4Gpb7GbRlWb1AOtPeBbJeMZqvt6jvlE+T\/+rFI0CksCcZ5YMGodbGWlVW4NiMTjAAiSffRKO\/21UyoGyCabPenmTX+x56KxAKey22Ym4SDjvNW+irL40E0AX8y2JqguDK+nuLMBUFx9ADXgrfYnQdsZxa2R8U8Pr5XvkRlSAjaCd49zfD7Y+wgFQQi9hKQ++ETkrMuH9zDAhE5lsCDFA8QViYgq49jtCeZmz2VP1gjjdvGgTeUrXdsvKehI6QJsnewBEabFeshg2c4SSK6zh53Sun9y3zt7bn\/pR49ez1\/Qp5JIOMmzNRMmcz5XIFF5Cqaftcd7DRdYIJGEsCgNVXjX0QyH4Q1sPZO00YTkq43mf\/\/nLA1f+YxNLvaMauYQJ0NqO0Mt2bmK19Hifh2iYhWVVM7LunkrjY15dIvZV2kJljMcNVlJUinSjtEApAkhB6e1cFX1C9tRHBrwFW7SN6sXMpH\/WAeVQOYDUC2jxlSc0\/+LIaA2aa0Vymva+fECDPhRxWtWTLFdo3Rk2ycbb6ygmwBlEvXiehDMcxiFYV10b+\/1oo6B098xZiJ2XJSaCa+FQRE9UsydvRHrJWRUF10YLnZfyU8pgy\/mQ6YUJVWYzneu\/WN2mwD9kQLIWHc=","accesskey":"ZQnAbZjCvNCjXY9rFSHtBct1XfkvOv5d6w9FLD5WPyddiDE584MbKL1na5X0Nao22bnsFRXu6bC8ZGO\/GJcaKw==","cachefile":"\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/","img":["\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150434_69980321034633_662.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150434_69980311754946_663.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150434_69987053516403_663.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150434_69987272421611_662.jpg"],"dstwidth":8000,"dstheight":4000,"isopt":0,"ismulti":1,"mutiluv":1}
15:05:29.549  [Thread-192, .lambda$startPhotoCapture$13(PreviewPresenter.java:3)]: 拍摄：循环询问相机是否可以拍照开始
15:05:29.560  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:05:32.561  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:05:34.766  [Thread-192, .lambda$startPhotoCapture$13(PreviewPresenter.java:13)]: 拍摄：循环询问相机是否可以拍照结束,相机准备好了：motorState=2
15:05:37.973 D/PhotoCapture [Thread-192, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:05:37.997  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:05:38.030  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:05:38.129  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:05:39.384  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:05:39.391  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=0
15:05:39.451  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：1
15:05:39.660  [Thread-197, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:05:39.666  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:05:39.678 D/StitchUtils [arch_disk_io_1, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:50)]: 拼接结束，拼接结果=0
15:05:40.998  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:05:43.999  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:05:44.358  [Thread-197, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=1
15:05:47.725 D/PhotoCapture [Thread-197, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:05:47.753  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:05:47.814  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:05:49.167  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:05:49.174  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=1
15:05:49.224  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：2
15:05:49.481  [Thread-201, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:05:49.497  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:05:49.503  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:05:50.754  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:05:53.755  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:05:54.047  [Thread-201, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=2
15:05:57.064 D/PhotoCapture [Thread-201, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:05:57.083  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:05:57.199  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:05:58.642  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:05:58.649  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=2
15:05:58.694  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
15:05:58.764  [Thread-205, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:05:58.849  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:05:59.085  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:06:00.084  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:06:03.085  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:06:03.556  [Thread-205, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=3
15:06:04.726 D/PhotoCapture [Thread-205, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:06:05.893  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:06:06.115  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:06:08.212  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:06:08.218  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=3
15:06:08.243  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
15:06:08.257  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:82)]: 拍摄：理论上上拍摄完毕（countTimes == 4）
15:06:08.263  [Thread-208, $PreviewHandler.lambda$handleMessage$0(PreviewPresenter.java:2)]: 拍摄：循环询问相机是否已复位开始
15:06:08.409  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:06:08.413  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:06:09.120  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:06:11.579 D/PbDownloadManager [arch_disk_io_6, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:5)]: 开始HDR，当前模式：多线程模式
15:06:11.583 D/PbDownloadManager [arch_disk_io_6, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:7)]: 开始HDR，多线程模式，线程池数量：2
15:06:12.122  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:06:15.123  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:06:16.108  [Thread-208, .cancelA6CaptureTimer(PreviewPresenter.java:2)]: 拍摄：中断计时器
15:06:16.114  [Thread-208, $PreviewHandler.lambda$handleMessage$0(PreviewPresenter.java:12)]: 拍摄：循环询问相机是否已复位结束：相机已复位
15:06:24.788 D/PbDownloadManager [arch_disk_io_6, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:27)]: HDR 结束：hdrList.size=4
15:06:24.920 D/StitchUtils [arch_disk_io_6, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:10)]: 从标定文件中获取到的标定参数：
15:06:24.926 D/StitchUtils [arch_disk_io_6, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:15)]: 获取临时标定参数失败：length=0; index=5
15:06:24.930 D/StitchUtils [arch_disk_io_6, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:48)]: 开始拼接，拼接参数={"calistr":"QnypDMewBPCJYTFZdJB5DbXyDbvGN1K6CiEFKbrVMoxHY\/R5DA0GKAe5YIAhHUogrWiQBN0tqR20hWrrR+NSC4Sx3mtU1O+5xWKkAHe0qH767tf5AsdTNkGv5WC7TAkWfQhDCUoSxXcye3xECq3Sm43k+nIb8cjLcRXEEuXj\/eSIxbZFZWoz+kTbzUaOfxeizTIr\/qfqVA5136gvq0rUah2gtsLNV7\/iuwraDjMzR5XIY4UOwyPVjywkH5XHtZbdVPD5Is2UjUjqct5yruYzKN5jcylCnWq+lwRKExfyri5jwJV2VYHJUA1mhaFz7pvJAThH1T0U2PllDyLY9NXcdTD7KkYIcyvV1oxDG3e5zI9LWYfFpTe2TrEgQMcuTPUeKVOY6fKiF79mCluQZ1PjPVuqvT3K4L4rtc7DOTnw58SH3+vcC6XeaWgxej1aIh9eg\/WJZllOnkIv+jOL8TTi2TuoJPJbGOscZUGunwQ2dAT26n5c1UJVAMaZNRnNldpRfWOK1S\/rw2e9F054ptiA\/Y\/1xGWDKmFKS+4bVhafZhcB8oO8PuXbO3Ur0xRYlVwiHDTSwpEm5T3RkS0MNUo9qGUruf5ae3vHgYK08+N3AuSumjvUBzr7\/JqET7+mDQQAHyQPo3Wu8PV4JmNTSmbF\/ZpktEEJfItSd1aPS+029qggSjdI8yw2\/jTXvtgVk3iWJXak2UFfVXteyKMb5zyO4hvw1ePCZ81Z1TPEik\/YJQ\/UBloBt+UHf9vpFocHco+0UiOYokhAYfFRAi9BZ17z7CtrhrpexratTPCaxpDz9j5m8Uk9EPRUJFferbFBPLlv2bIghEpCn9clj4dzJzKuSwR0Hilx8Q4cg45W6D6EbsC2hxP3FvRK\/W+Ft7jxIrY\/Qv6EiIuOPf\/C6miLcGobOCHkxbW7\/DO0VCsfdDI9dysYQtdhIG8iM2cBUaWqYnkCM5\/B1jVQMnjlvaVvR75\/BRG9WAfBZXjpLb+KNTvcQ6Nud2KckMT0R\/JLspQ0opScQZW5o4LkB5HkTP4HPU6RUY6lCaa7QfUgxAAckBTHb7slu4Gpb7GbRlWb1AOtPeBbJeMZqvt6jvlE+T\/+rFI0CksCcZ5YMGodbGWlVW4NiMTjAAiSffRKO\/21UyoGyCabPenmTX+x56KxAKey22Ym4SDjvNW+irL40E0AX8y2JqguDK+nuLMBUFx9ADXgrfYnQdsZxa2R8U8Pr5XvkRlSAjaCd49zfD7Y+wgFQQi9hKQ++ETkrMuH9zDAhE5lsCDFA8QViYgq49jtCeZmz2VP1gjjdvGgTeUrXdsvKehI6QJsnewBEabFeshg2c4SSK6zh53Sun9y3zt7bn\/pR49ez1\/Qp5JIOMmzNRMmcz5XIFF5Cqaftcd7DRdYIJGEsCgNVXjX0QyH4Q1sPZO00YTkq43mf\/\/nLA1f+YxNLvaMauYQJ0NqO0Mt2bmK19Hifh2iYhWVVM7LunkrjY15dIvZV2kJljMcNVlJUinSjtEApAkhB6e1cFX1C9tRHBrwFW7SN6sXMpH\/WAeVQOYDUC2jxlSc0\/+LIaA2aa0Vymva+fECDPhRxWtWTLFdo3Rk2ycbb6ygmwBlEvXiehDMcxiFYV10b+\/1oo6B098xZiJ2XJSaCa+FQRE9UsydvRHrJWRUF10YLnZfyU8pgy\/mQ6YUJVWYzneu\/WN2mwD9kQLIWHc=","accesskey":"ZQnAbZjCvNCjXY9rFSHtBct1XfkvOv5d6w9FLD5WPyddiDE584MbKL1na5X0Nao22bnsFRXu6bC8ZGO\/GJcaKw==","cachefile":"\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/","img":["\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150535_70036291923687_694.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150535_70036286521604_695.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150535_70042756082540_695.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150535_70042669537228_694.jpg"],"dstwidth":8000,"dstheight":4000,"isopt":0,"ismulti":1,"mutiluv":1}
15:06:35.159 D/StitchUtils [arch_disk_io_6, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:50)]: 拼接结束，拼接结果=0
15:06:45.296  [main, .startOrStopCapture(PreviewPresenter.java:1)]: 拍摄：点击拍摄按钮
15:06:45.455  [main, .startPhotoCapture(PreviewPresenter.java:1)]: 拍摄：开始拍摄
15:06:45.537  [main, .cancelA6CaptureTimer(PreviewPresenter.java:2)]: 拍摄：中断计时器
15:06:45.539  [main, .startA6CaptureTimer(PreviewPresenter.java:6)]: 拍摄：A6 开启计时器
15:06:45.545  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 0
15:06:45.611  [Thread-210, .lambda$startPhotoCapture$13(PreviewPresenter.java:3)]: 拍摄：循环询问相机是否可以拍照开始
15:06:48.546  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:06:50.726  [Thread-210, .lambda$startPhotoCapture$13(PreviewPresenter.java:13)]: 拍摄：循环询问相机是否可以拍照结束,相机准备好了：motorState=2
15:06:51.929 D/PhotoCapture [Thread-210, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:06:51.945  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:06:53.103  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:06:54.985  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:06:55.312  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:06:55.318  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=0
15:06:55.367  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：1
15:06:55.552  [Thread-215, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:06:55.560  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:06:55.567  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:06:57.986  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:07:00.143  [Thread-215, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=1
15:07:03.706 D/PhotoCapture [Thread-215, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:07:03.737  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:07:03.831  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:07:04.915  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:07:04.920  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=1
15:07:04.958  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：2
15:07:05.313  [Thread-219, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:07:05.323  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:07:05.331  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:07:06.739  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:07:09.740  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:07:09.752  [Thread-219, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=2
15:07:12.736 D/PhotoCapture [Thread-219, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:07:12.764  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:07:12.794  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:07:14.885  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:07:14.893  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=2
15:07:14.901  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
15:07:15.035  [Thread-223, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:07:15.042  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:07:15.047  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:07:15.766  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:07:18.767  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:07:19.808  [Thread-223, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=3
15:07:23.060 D/PhotoCapture [Thread-223, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:07:23.078  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:07:23.166  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:07:24.802  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:07:24.811  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=3
15:07:24.884  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
15:07:24.895  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:82)]: 拍摄：理论上上拍摄完毕（countTimes == 4）
15:07:24.910  [Thread-226, $PreviewHandler.lambda$handleMessage$0(PreviewPresenter.java:2)]: 拍摄：循环询问相机是否已复位开始
15:07:25.119  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:07:25.124  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:07:26.101  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:07:27.956 D/PbDownloadManager [arch_disk_io_4, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:5)]: 开始HDR，当前模式：多线程模式
15:07:27.960 D/PbDownloadManager [arch_disk_io_4, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:7)]: 开始HDR，多线程模式，线程池数量：2
15:07:29.102  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:07:32.114  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:07:32.807  [Thread-226, .cancelA6CaptureTimer(PreviewPresenter.java:2)]: 拍摄：中断计时器
15:07:32.810  [Thread-226, $PreviewHandler.lambda$handleMessage$0(PreviewPresenter.java:12)]: 拍摄：循环询问相机是否已复位结束：相机已复位
15:07:40.958 D/PbDownloadManager [arch_disk_io_4, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:27)]: HDR 结束：hdrList.size=4
15:07:41.089 D/StitchUtils [arch_disk_io_4, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:10)]: 从标定文件中获取到的标定参数：
15:07:41.092 D/StitchUtils [arch_disk_io_4, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:15)]: 获取临时标定参数失败：length=0; index=5
15:07:41.095 D/StitchUtils [arch_disk_io_4, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:48)]: 开始拼接，拼接参数={"calistr":"QnypDMewBPCJYTFZdJB5DbXyDbvGN1K6CiEFKbrVMoxHY\/R5DA0GKAe5YIAhHUogrWiQBN0tqR20hWrrR+NSC4Sx3mtU1O+5xWKkAHe0qH767tf5AsdTNkGv5WC7TAkWfQhDCUoSxXcye3xECq3Sm43k+nIb8cjLcRXEEuXj\/eSIxbZFZWoz+kTbzUaOfxeizTIr\/qfqVA5136gvq0rUah2gtsLNV7\/iuwraDjMzR5XIY4UOwyPVjywkH5XHtZbdVPD5Is2UjUjqct5yruYzKN5jcylCnWq+lwRKExfyri5jwJV2VYHJUA1mhaFz7pvJAThH1T0U2PllDyLY9NXcdTD7KkYIcyvV1oxDG3e5zI9LWYfFpTe2TrEgQMcuTPUeKVOY6fKiF79mCluQZ1PjPVuqvT3K4L4rtc7DOTnw58SH3+vcC6XeaWgxej1aIh9eg\/WJZllOnkIv+jOL8TTi2TuoJPJbGOscZUGunwQ2dAT26n5c1UJVAMaZNRnNldpRfWOK1S\/rw2e9F054ptiA\/Y\/1xGWDKmFKS+4bVhafZhcB8oO8PuXbO3Ur0xRYlVwiHDTSwpEm5T3RkS0MNUo9qGUruf5ae3vHgYK08+N3AuSumjvUBzr7\/JqET7+mDQQAHyQPo3Wu8PV4JmNTSmbF\/ZpktEEJfItSd1aPS+029qggSjdI8yw2\/jTXvtgVk3iWJXak2UFfVXteyKMb5zyO4hvw1ePCZ81Z1TPEik\/YJQ\/UBloBt+UHf9vpFocHco+0UiOYokhAYfFRAi9BZ17z7CtrhrpexratTPCaxpDz9j5m8Uk9EPRUJFferbFBPLlv2bIghEpCn9clj4dzJzKuSwR0Hilx8Q4cg45W6D6EbsC2hxP3FvRK\/W+Ft7jxIrY\/Qv6EiIuOPf\/C6miLcGobOCHkxbW7\/DO0VCsfdDI9dysYQtdhIG8iM2cBUaWqYnkCM5\/B1jVQMnjlvaVvR75\/BRG9WAfBZXjpLb+KNTvcQ6Nud2KckMT0R\/JLspQ0opScQZW5o4LkB5HkTP4HPU6RUY6lCaa7QfUgxAAckBTHb7slu4Gpb7GbRlWb1AOtPeBbJeMZqvt6jvlE+T\/+rFI0CksCcZ5YMGodbGWlVW4NiMTjAAiSffRKO\/21UyoGyCabPenmTX+x56KxAKey22Ym4SDjvNW+irL40E0AX8y2JqguDK+nuLMBUFx9ADXgrfYnQdsZxa2R8U8Pr5XvkRlSAjaCd49zfD7Y+wgFQQi9hKQ++ETkrMuH9zDAhE5lsCDFA8QViYgq49jtCeZmz2VP1gjjdvGgTeUrXdsvKehI6QJsnewBEabFeshg2c4SSK6zh53Sun9y3zt7bn\/pR49ez1\/Qp5JIOMmzNRMmcz5XIFF5Cqaftcd7DRdYIJGEsCgNVXjX0QyH4Q1sPZO00YTkq43mf\/\/nLA1f+YxNLvaMauYQJ0NqO0Mt2bmK19Hifh2iYhWVVM7LunkrjY15dIvZV2kJljMcNVlJUinSjtEApAkhB6e1cFX1C9tRHBrwFW7SN6sXMpH\/WAeVQOYDUC2jxlSc0\/+LIaA2aa0Vymva+fECDPhRxWtWTLFdo3Rk2ycbb6ygmwBlEvXiehDMcxiFYV10b+\/1oo6B098xZiJ2XJSaCa+FQRE9UsydvRHrJWRUF10YLnZfyU8pgy\/mQ6YUJVWYzneu\/WN2mwD9kQLIWHc=","accesskey":"ZQnAbZjCvNCjXY9rFSHtBct1XfkvOv5d6w9FLD5WPyddiDE584MbKL1na5X0Nao22bnsFRXu6bC8ZGO\/GJcaKw==","cachefile":"\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/","img":["\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150651_70112731413780_727.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150651_70112531765863_728.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150651_70118924049716_728.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150651_70118863358570_727.jpg"],"dstwidth":8000,"dstheight":4000,"isopt":0,"ismulti":1,"mutiluv":1}
15:07:51.559 D/StitchUtils [arch_disk_io_4, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:50)]: 拼接结束，拼接结果=0
15:08:26.804  [main, .startOrStopCapture(PreviewPresenter.java:1)]: 拍摄：点击拍摄按钮
15:08:26.972  [main, .startPhotoCapture(PreviewPresenter.java:1)]: 拍摄：开始拍摄
15:08:27.051  [main, .cancelA6CaptureTimer(PreviewPresenter.java:2)]: 拍摄：中断计时器
15:08:27.053  [main, .startA6CaptureTimer(PreviewPresenter.java:6)]: 拍摄：A6 开启计时器
15:08:27.057  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 0
15:08:27.152  [Thread-228, .lambda$startPhotoCapture$13(PreviewPresenter.java:3)]: 拍摄：循环询问相机是否可以拍照开始
15:08:30.059  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:08:32.221  [Thread-228, .lambda$startPhotoCapture$13(PreviewPresenter.java:13)]: 拍摄：循环询问相机是否可以拍照结束,相机准备好了：motorState=2
15:08:35.773 D/PhotoCapture [Thread-228, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:08:35.805  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:08:36.065  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:08:37.078  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:08:37.084  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=0
15:08:37.117  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：1
15:08:37.223  [Thread-233, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:08:37.326  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:08:37.510  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:08:38.806  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:08:41.807  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:08:41.983  [Thread-233, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=1
15:08:45.416 D/PhotoCapture [Thread-233, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:08:45.442  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:08:45.496  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:08:45.712  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:08:46.697  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:08:46.703  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=1
15:08:46.776  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：2
15:08:46.985  [Thread-237, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:08:46.992  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:08:48.443  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:08:51.444  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:08:51.763  [Thread-237, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=2
15:08:54.888 D/PhotoCapture [Thread-237, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:08:54.907  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:08:54.948  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:08:56.146  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:08:56.154  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=2
15:08:56.207  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
15:08:56.492  [Thread-241, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:08:56.504  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:08:56.577  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:08:57.909  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:09:00.910  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:09:01.126  [Thread-241, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=3
15:09:04.743 D/PhotoCapture [Thread-241, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:09:04.755  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:09:05.058  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:09:06.070  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:09:06.079  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=3
15:09:06.115  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
15:09:06.129  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:82)]: 拍摄：理论上上拍摄完毕（countTimes == 4）
15:09:06.134  [Thread-244, $PreviewHandler.lambda$handleMessage$0(PreviewPresenter.java:2)]: 拍摄：循环询问相机是否已复位开始
15:09:06.617  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:09:06.625  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:09:07.787  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:09:09.782 D/PbDownloadManager [arch_disk_io_7, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:5)]: 开始HDR，当前模式：多线程模式
15:09:09.786 D/PbDownloadManager [arch_disk_io_7, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:7)]: 开始HDR，多线程模式，线程池数量：2
15:09:10.789  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:09:13.796  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:09:14.058  [Thread-244, .cancelA6CaptureTimer(PreviewPresenter.java:2)]: 拍摄：中断计时器
15:09:14.063  [Thread-244, $PreviewHandler.lambda$handleMessage$0(PreviewPresenter.java:12)]: 拍摄：循环询问相机是否已复位结束：相机已复位
15:09:22.964 D/PbDownloadManager [arch_disk_io_7, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:27)]: HDR 结束：hdrList.size=4
15:09:23.106 D/StitchUtils [arch_disk_io_7, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:10)]: 从标定文件中获取到的标定参数：
15:09:23.114 D/StitchUtils [arch_disk_io_7, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:15)]: 获取临时标定参数失败：length=0; index=5
15:09:23.119 D/StitchUtils [arch_disk_io_7, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:48)]: 开始拼接，拼接参数={"calistr":"QnypDMewBPCJYTFZdJB5DbXyDbvGN1K6CiEFKbrVMoxHY\/R5DA0GKAe5YIAhHUogrWiQBN0tqR20hWrrR+NSC4Sx3mtU1O+5xWKkAHe0qH767tf5AsdTNkGv5WC7TAkWfQhDCUoSxXcye3xECq3Sm43k+nIb8cjLcRXEEuXj\/eSIxbZFZWoz+kTbzUaOfxeizTIr\/qfqVA5136gvq0rUah2gtsLNV7\/iuwraDjMzR5XIY4UOwyPVjywkH5XHtZbdVPD5Is2UjUjqct5yruYzKN5jcylCnWq+lwRKExfyri5jwJV2VYHJUA1mhaFz7pvJAThH1T0U2PllDyLY9NXcdTD7KkYIcyvV1oxDG3e5zI9LWYfFpTe2TrEgQMcuTPUeKVOY6fKiF79mCluQZ1PjPVuqvT3K4L4rtc7DOTnw58SH3+vcC6XeaWgxej1aIh9eg\/WJZllOnkIv+jOL8TTi2TuoJPJbGOscZUGunwQ2dAT26n5c1UJVAMaZNRnNldpRfWOK1S\/rw2e9F054ptiA\/Y\/1xGWDKmFKS+4bVhafZhcB8oO8PuXbO3Ur0xRYlVwiHDTSwpEm5T3RkS0MNUo9qGUruf5ae3vHgYK08+N3AuSumjvUBzr7\/JqET7+mDQQAHyQPo3Wu8PV4JmNTSmbF\/ZpktEEJfItSd1aPS+029qggSjdI8yw2\/jTXvtgVk3iWJXak2UFfVXteyKMb5zyO4hvw1ePCZ81Z1TPEik\/YJQ\/UBloBt+UHf9vpFocHco+0UiOYokhAYfFRAi9BZ17z7CtrhrpexratTPCaxpDz9j5m8Uk9EPRUJFferbFBPLlv2bIghEpCn9clj4dzJzKuSwR0Hilx8Q4cg45W6D6EbsC2hxP3FvRK\/W+Ft7jxIrY\/Qv6EiIuOPf\/C6miLcGobOCHkxbW7\/DO0VCsfdDI9dysYQtdhIG8iM2cBUaWqYnkCM5\/B1jVQMnjlvaVvR75\/BRG9WAfBZXjpLb+KNTvcQ6Nud2KckMT0R\/JLspQ0opScQZW5o4LkB5HkTP4HPU6RUY6lCaa7QfUgxAAckBTHb7slu4Gpb7GbRlWb1AOtPeBbJeMZqvt6jvlE+T\/+rFI0CksCcZ5YMGodbGWlVW4NiMTjAAiSffRKO\/21UyoGyCabPenmTX+x56KxAKey22Ym4SDjvNW+irL40E0AX8y2JqguDK+nuLMBUFx9ADXgrfYnQdsZxa2R8U8Pr5XvkRlSAjaCd49zfD7Y+wgFQQi9hKQ++ETkrMuH9zDAhE5lsCDFA8QViYgq49jtCeZmz2VP1gjjdvGgTeUrXdsvKehI6QJsnewBEabFeshg2c4SSK6zh53Sun9y3zt7bn\/pR49ez1\/Qp5JIOMmzNRMmcz5XIFF5Cqaftcd7DRdYIJGEsCgNVXjX0QyH4Q1sPZO00YTkq43mf\/\/nLA1f+YxNLvaMauYQJ0NqO0Mt2bmK19Hifh2iYhWVVM7LunkrjY15dIvZV2kJljMcNVlJUinSjtEApAkhB6e1cFX1C9tRHBrwFW7SN6sXMpH\/WAeVQOYDUC2jxlSc0\/+LIaA2aa0Vymva+fECDPhRxWtWTLFdo3Rk2ycbb6ygmwBlEvXiehDMcxiFYV10b+\/1oo6B098xZiJ2XJSaCa+FQRE9UsydvRHrJWRUF10YLnZfyU8pgy\/mQ6YUJVWYzneu\/WN2mwD9kQLIWHc=","accesskey":"ZQnAbZjCvNCjXY9rFSHtBct1XfkvOv5d6w9FLD5WPyddiDE584MbKL1na5X0Nao22bnsFRXu6bC8ZGO\/GJcaKw==","cachefile":"\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/","img":["\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150833_70214599676264_759.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150833_70214501600222_760.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150833_70220934914805_760.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150833_70220932204388_759.jpg"],"dstwidth":8000,"dstheight":4000,"isopt":0,"ismulti":1,"mutiluv":1}
15:09:33.272 D/StitchUtils [arch_disk_io_7, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:50)]: 拼接结束，拼接结果=0
15:09:53.181  [main, .startOrStopCapture(PreviewPresenter.java:1)]: 拍摄：点击拍摄按钮
15:09:53.367  [main, .startPhotoCapture(PreviewPresenter.java:1)]: 拍摄：开始拍摄
15:09:53.478  [main, .cancelA6CaptureTimer(PreviewPresenter.java:2)]: 拍摄：中断计时器
15:09:53.480  [main, .startA6CaptureTimer(PreviewPresenter.java:6)]: 拍摄：A6 开启计时器
15:09:53.486  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 0
15:09:53.752  [Thread-246, .lambda$startPhotoCapture$13(PreviewPresenter.java:3)]: 拍摄：循环询问相机是否可以拍照开始
15:09:56.486  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:09:58.771  [Thread-246, .lambda$startPhotoCapture$13(PreviewPresenter.java:13)]: 拍摄：循环询问相机是否可以拍照结束,相机准备好了：motorState=2
15:10:01.595 D/PhotoCapture [Thread-246, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:10:01.613  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:10:01.872  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:10:03.309  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:10:03.318  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=0
15:10:03.361  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：1
15:10:03.481  [Thread-251, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:10:03.557  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:10:03.733  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:10:04.614  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:10:07.616  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:10:08.258  [Thread-251, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=1
15:10:09.377 D/PhotoCapture [Thread-251, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:10:10.999  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:10:11.048  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:10:12.718  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:10:12.722  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=1
15:10:12.766  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：2
15:10:12.941  [Thread-255, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:10:12.948  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:10:12.985  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:10:14.001  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:10:17.007  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:10:17.582  [Thread-255, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=2
15:10:20.841 D/PhotoCapture [Thread-255, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:10:20.862  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:10:20.928  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:10:22.365  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:10:22.374  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=2
15:10:22.408  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
15:10:22.509  [Thread-259, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:10:22.589  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:10:22.732  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:10:23.863  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:10:26.865  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:10:27.331  [Thread-259, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=3
15:10:30.499 D/PhotoCapture [Thread-259, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:10:30.512  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:10:30.658  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:10:32.339  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:10:32.346  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=3
15:10:32.394  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
15:10:32.404  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:82)]: 拍摄：理论上上拍摄完毕（countTimes == 4）
15:10:32.410  [Thread-262, $PreviewHandler.lambda$handleMessage$0(PreviewPresenter.java:2)]: 拍摄：循环询问相机是否已复位开始
15:10:32.614  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:10:32.624  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:10:33.513  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:10:36.026 D/PbDownloadManager [arch_disk_io_2, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:5)]: 开始HDR，当前模式：多线程模式
15:10:36.034 D/PbDownloadManager [arch_disk_io_2, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:7)]: 开始HDR，多线程模式，线程池数量：2
15:10:36.514  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:10:39.516  [Thread-119, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:10:40.360  [Thread-262, .cancelA6CaptureTimer(PreviewPresenter.java:2)]: 拍摄：中断计时器
15:10:40.369  [Thread-262, $PreviewHandler.lambda$handleMessage$0(PreviewPresenter.java:12)]: 拍摄：循环询问相机是否已复位结束：相机已复位
15:10:49.272 D/PbDownloadManager [arch_disk_io_2, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:27)]: HDR 结束：hdrList.size=4
15:10:49.415 D/StitchUtils [arch_disk_io_2, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:10)]: 从标定文件中获取到的标定参数：
15:10:49.421 D/StitchUtils [arch_disk_io_2, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:15)]: 获取临时标定参数失败：length=0; index=5
15:10:49.428 D/StitchUtils [arch_disk_io_2, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:48)]: 开始拼接，拼接参数={"calistr":"QnypDMewBPCJYTFZdJB5DbXyDbvGN1K6CiEFKbrVMoxHY\/R5DA0GKAe5YIAhHUogrWiQBN0tqR20hWrrR+NSC4Sx3mtU1O+5xWKkAHe0qH767tf5AsdTNkGv5WC7TAkWfQhDCUoSxXcye3xECq3Sm43k+nIb8cjLcRXEEuXj\/eSIxbZFZWoz+kTbzUaOfxeizTIr\/qfqVA5136gvq0rUah2gtsLNV7\/iuwraDjMzR5XIY4UOwyPVjywkH5XHtZbdVPD5Is2UjUjqct5yruYzKN5jcylCnWq+lwRKExfyri5jwJV2VYHJUA1mhaFz7pvJAThH1T0U2PllDyLY9NXcdTD7KkYIcyvV1oxDG3e5zI9LWYfFpTe2TrEgQMcuTPUeKVOY6fKiF79mCluQZ1PjPVuqvT3K4L4rtc7DOTnw58SH3+vcC6XeaWgxej1aIh9eg\/WJZllOnkIv+jOL8TTi2TuoJPJbGOscZUGunwQ2dAT26n5c1UJVAMaZNRnNldpRfWOK1S\/rw2e9F054ptiA\/Y\/1xGWDKmFKS+4bVhafZhcB8oO8PuXbO3Ur0xRYlVwiHDTSwpEm5T3RkS0MNUo9qGUruf5ae3vHgYK08+N3AuSumjvUBzr7\/JqET7+mDQQAHyQPo3Wu8PV4JmNTSmbF\/ZpktEEJfItSd1aPS+029qggSjdI8yw2\/jTXvtgVk3iWJXak2UFfVXteyKMb5zyO4hvw1ePCZ81Z1TPEik\/YJQ\/UBloBt+UHf9vpFocHco+0UiOYokhAYfFRAi9BZ17z7CtrhrpexratTPCaxpDz9j5m8Uk9EPRUJFferbFBPLlv2bIghEpCn9clj4dzJzKuSwR0Hilx8Q4cg45W6D6EbsC2hxP3FvRK\/W+Ft7jxIrY\/Qv6EiIuOPf\/C6miLcGobOCHkxbW7\/DO0VCsfdDI9dysYQtdhIG8iM2cBUaWqYnkCM5\/B1jVQMnjlvaVvR75\/BRG9WAfBZXjpLb+KNTvcQ6Nud2KckMT0R\/JLspQ0opScQZW5o4LkB5HkTP4HPU6RUY6lCaa7QfUgxAAckBTHb7slu4Gpb7GbRlWb1AOtPeBbJeMZqvt6jvlE+T\/+rFI0CksCcZ5YMGodbGWlVW4NiMTjAAiSffRKO\/21UyoGyCabPenmTX+x56KxAKey22Ym4SDjvNW+irL40E0AX8y2JqguDK+nuLMBUFx9ADXgrfYnQdsZxa2R8U8Pr5XvkRlSAjaCd49zfD7Y+wgFQQi9hKQ++ETkrMuH9zDAhE5lsCDFA8QViYgq49jtCeZmz2VP1gjjdvGgTeUrXdsvKehI6QJsnewBEabFeshg2c4SSK6zh53Sun9y3zt7bn\/pR49ez1\/Qp5JIOMmzNRMmcz5XIFF5Cqaftcd7DRdYIJGEsCgNVXjX0QyH4Q1sPZO00YTkq43mf\/\/nLA1f+YxNLvaMauYQJ0NqO0Mt2bmK19Hifh2iYhWVVM7LunkrjY15dIvZV2kJljMcNVlJUinSjtEApAkhB6e1cFX1C9tRHBrwFW7SN6sXMpH\/WAeVQOYDUC2jxlSc0\/+LIaA2aa0Vymva+fECDPhRxWtWTLFdo3Rk2ycbb6ygmwBlEvXiehDMcxiFYV10b+\/1oo6B098xZiJ2XJSaCa+FQRE9UsydvRHrJWRUF10YLnZfyU8pgy\/mQ6YUJVWYzneu\/WN2mwD9kQLIWHc=","accesskey":"ZQnAbZjCvNCjXY9rFSHtBct1XfkvOv5d6w9FLD5WPyddiDE584MbKL1na5X0Nao22bnsFRXu6bC8ZGO\/GJcaKw==","cachefile":"\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/","img":["\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150959_70300731727813_791.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150959_70300764862188_792.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150959_70307149301250_791.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_150959_70307244620521_792.jpg"],"dstwidth":8000,"dstheight":4000,"isopt":0,"ismulti":1,"mutiluv":1}
15:10:59.576 D/StitchUtils [arch_disk_io_2, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:50)]: 拼接结束，拼接结果=0
15:12:11.732 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, Security type: -1, Supplicant state: COMPLETED, Wi-Fi standard: 4, RSSI: -48, Link speed: 52Mbps, Tx Link speed: 52Mbps, Max Supported Tx Link speed: 72Mbps, Rx Link speed: 65Mbps, Max Supported Rx Link speed: 72Mbps, Frequency: 2437MHz, Net ID: -1, Metered hint: false, score: 60, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1
15:12:20.199 D/HwAccelChecker [main, com.icatch.mobilecam.utils.HwAccelChecker.detectSupportedHwEncoder(HwAccelChecker.java:21)]: Found HW H.264 Encoder: OMX.hisi.video.encoder.avc (Types: [video/avc])
15:12:20.203 E/VideoUtil [main, com.icatch.mobilecam.utils.VideoUtil.getVideoResolution(VideoUtil.java:2)]: args[0] = VideoUtils
args[1] = 不是视频：/storage/emulated/0/DCIM/IJOYER/20250801_150959_HDR.JPG

15:12:20.281 D/PanoramaPlayerActivity [main, com.ijoyer.camera.activity.PanoramaPlayerActivity$7.onSuccess(PanoramaPlayerActivity.java:4)]: PanoramaPlayerActivity HMS Image Vision API 初始化成功
15:12:29.007 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, Security type: -1, Supplicant state: COMPLETED, Wi-Fi standard: 4, RSSI: -48, Link speed: 52Mbps, Tx Link speed: 52Mbps, Max Supported Tx Link speed: 72Mbps, Rx Link speed: 65Mbps, Max Supported Rx Link speed: 72Mbps, Frequency: 2437MHz, Net ID: -1, Metered hint: false, score: 60, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1
15:12:49.440  [main, .refreshBatteryLevel(PreviewPresenter.java:4)]: 刷新电池电量：100
15:12:49.759  [main, .refreshBatteryLevel(PreviewPresenter.java:4)]: 刷新电池电量：100
15:13:07.143 D/PreviewActivity [main, com.icatch.mobilecam.ui.activity.PreviewActivity.onDestroy(PreviewActivity.java:4)]: PreviewActivity:正常销毁
15:13:30.123 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, Security type: -1, Supplicant state: COMPLETED, Wi-Fi standard: 4, RSSI: -48, Link speed: 52Mbps, Tx Link speed: 52Mbps, Max Supported Tx Link speed: 72Mbps, Rx Link speed: 65Mbps, Max Supported Rx Link speed: 72Mbps, Frequency: 2437MHz, Net ID: -1, Metered hint: false, score: 60, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1
15:13:30.124 D/MainActivity [main, com.ijoyer.camera.activity.MainActivity.goToNext(MainActivity.java:3)]: 进入：lastExitPreviewActivityTime=23438
15:13:31.673  [main, .refreshBatteryLevel(PreviewPresenter.java:4)]: 刷新电池电量：100
15:13:32.002  [main, .refreshBatteryLevel(PreviewPresenter.java:4)]: 刷新电池电量：100
15:13:37.598  [main, .startOrStopCapture(PreviewPresenter.java:1)]: 拍摄：点击拍摄按钮
15:13:37.776  [main, .startPhotoCapture(PreviewPresenter.java:1)]: 拍摄：开始拍摄
15:13:37.840  [main, .cancelA6CaptureTimer(PreviewPresenter.java:2)]: 拍摄：中断计时器
15:13:37.843  [main, .startA6CaptureTimer(PreviewPresenter.java:6)]: 拍摄：A6 开启计时器
15:13:37.847  [Thread-295, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 0
15:13:38.067  [Thread-296, .lambda$startPhotoCapture$13(PreviewPresenter.java:3)]: 拍摄：循环询问相机是否可以拍照开始
15:13:40.849  [Thread-295, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:13:43.175  [Thread-296, .lambda$startPhotoCapture$13(PreviewPresenter.java:13)]: 拍摄：循环询问相机是否可以拍照结束,相机准备好了：motorState=2
15:13:46.348 D/PhotoCapture [Thread-296, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:13:46.366  [Thread-295, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:13:46.429  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:13:48.035  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:13:48.042  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=0
15:13:48.119  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：1
15:13:48.292  [Thread-301, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:13:48.299  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:13:48.347  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:13:49.369  [Thread-295, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:13:52.370  [Thread-295, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:13:52.998  [Thread-301, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=1
15:13:56.647 D/PhotoCapture [Thread-301, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:13:56.658  [Thread-295, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:13:56.822  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:13:56.896  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:13:57.439  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:13:57.445  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=1
15:13:57.520  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：2
15:13:57.723  [Thread-305, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:13:57.735  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:13:59.663  [Thread-295, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:14:02.341  [Thread-305, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=2
15:14:05.496 D/PhotoCapture [Thread-305, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:14:05.541  [Thread-295, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:14:05.608  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:14:07.171  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:14:07.177  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=2
15:14:07.341  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
15:14:07.564  [Thread-309, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
15:14:07.569  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:14:07.574  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:14:08.542  [Thread-295, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:14:11.543  [Thread-295, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:14:12.052  [Thread-309, $PreviewHandler.lambda$handleMessage$3(PreviewPresenter.java:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=3
15:14:15.233 D/PhotoCapture [Thread-309, com.icatch.mobilecam.Function.CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
15:14:15.256  [Thread-295, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 2
15:14:15.319  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:14:16.974  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：1
15:14:16.980  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=3
15:14:17.021  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
15:14:17.033  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:82)]: 拍摄：理论上上拍摄完毕（countTimes == 4）
15:14:17.040  [Thread-312, $PreviewHandler.lambda$handleMessage$0(PreviewPresenter.java:2)]: 拍摄：循环询问相机是否已复位开始
15:14:17.235  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:14:17.268  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：7
15:14:18.325  [Thread-295, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:14:19.987 D/PbDownloadManager [arch_disk_io_6, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:5)]: 开始HDR，当前模式：多线程模式
15:14:19.992 D/PbDownloadManager [arch_disk_io_6, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:7)]: 开始HDR，多线程模式，线程池数量：2
15:14:21.326  [Thread-295, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:14:24.327  [Thread-295, .lambda$startA6CaptureTimer$19(PreviewPresenter.java:6)]: 拍摄：motorState = 1
15:14:24.966  [Thread-312, .cancelA6CaptureTimer(PreviewPresenter.java:2)]: 拍摄：中断计时器
15:14:24.969  [Thread-312, $PreviewHandler.lambda$handleMessage$0(PreviewPresenter.java:12)]: 拍摄：循环询问相机是否已复位结束：相机已复位
15:14:33.112 D/PbDownloadManager [arch_disk_io_6, com.icatch.mobilecam.Function.CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:27)]: HDR 结束：hdrList.size=4
15:14:33.258 D/StitchUtils [arch_disk_io_6, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:10)]: 从标定文件中获取到的标定参数：
15:14:33.263 D/StitchUtils [arch_disk_io_6, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:15)]: 获取临时标定参数失败：length=0; index=5
15:14:33.268 D/StitchUtils [arch_disk_io_6, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:48)]: 开始拼接，拼接参数={"calistr":"QnypDMewBPCJYTFZdJB5DbXyDbvGN1K6CiEFKbrVMoxHY\/R5DA0GKAe5YIAhHUogrWiQBN0tqR20hWrrR+NSC4Sx3mtU1O+5xWKkAHe0qH767tf5AsdTNkGv5WC7TAkWfQhDCUoSxXcye3xECq3Sm43k+nIb8cjLcRXEEuXj\/eSIxbZFZWoz+kTbzUaOfxeizTIr\/qfqVA5136gvq0rUah2gtsLNV7\/iuwraDjMzR5XIY4UOwyPVjywkH5XHtZbdVPD5Is2UjUjqct5yruYzKN5jcylCnWq+lwRKExfyri5jwJV2VYHJUA1mhaFz7pvJAThH1T0U2PllDyLY9NXcdTD7KkYIcyvV1oxDG3e5zI9LWYfFpTe2TrEgQMcuTPUeKVOY6fKiF79mCluQZ1PjPVuqvT3K4L4rtc7DOTnw58SH3+vcC6XeaWgxej1aIh9eg\/WJZllOnkIv+jOL8TTi2TuoJPJbGOscZUGunwQ2dAT26n5c1UJVAMaZNRnNldpRfWOK1S\/rw2e9F054ptiA\/Y\/1xGWDKmFKS+4bVhafZhcB8oO8PuXbO3Ur0xRYlVwiHDTSwpEm5T3RkS0MNUo9qGUruf5ae3vHgYK08+N3AuSumjvUBzr7\/JqET7+mDQQAHyQPo3Wu8PV4JmNTSmbF\/ZpktEEJfItSd1aPS+029qggSjdI8yw2\/jTXvtgVk3iWJXak2UFfVXteyKMb5zyO4hvw1ePCZ81Z1TPEik\/YJQ\/UBloBt+UHf9vpFocHco+0UiOYokhAYfFRAi9BZ17z7CtrhrpexratTPCaxpDz9j5m8Uk9EPRUJFferbFBPLlv2bIghEpCn9clj4dzJzKuSwR0Hilx8Q4cg45W6D6EbsC2hxP3FvRK\/W+Ft7jxIrY\/Qv6EiIuOPf\/C6miLcGobOCHkxbW7\/DO0VCsfdDI9dysYQtdhIG8iM2cBUaWqYnkCM5\/B1jVQMnjlvaVvR75\/BRG9WAfBZXjpLb+KNTvcQ6Nud2KckMT0R\/JLspQ0opScQZW5o4LkB5HkTP4HPU6RUY6lCaa7QfUgxAAckBTHb7slu4Gpb7GbRlWb1AOtPeBbJeMZqvt6jvlE+T\/+rFI0CksCcZ5YMGodbGWlVW4NiMTjAAiSffRKO\/21UyoGyCabPenmTX+x56KxAKey22Ym4SDjvNW+irL40E0AX8y2JqguDK+nuLMBUFx9ADXgrfYnQdsZxa2R8U8Pr5XvkRlSAjaCd49zfD7Y+wgFQQi9hKQ++ETkrMuH9zDAhE5lsCDFA8QViYgq49jtCeZmz2VP1gjjdvGgTeUrXdsvKehI6QJsnewBEabFeshg2c4SSK6zh53Sun9y3zt7bn\/pR49ez1\/Qp5JIOMmzNRMmcz5XIFF5Cqaftcd7DRdYIJGEsCgNVXjX0QyH4Q1sPZO00YTkq43mf\/\/nLA1f+YxNLvaMauYQJ0NqO0Mt2bmK19Hifh2iYhWVVM7LunkrjY15dIvZV2kJljMcNVlJUinSjtEApAkhB6e1cFX1C9tRHBrwFW7SN6sXMpH\/WAeVQOYDUC2jxlSc0\/+LIaA2aa0Vymva+fECDPhRxWtWTLFdo3Rk2ycbb6ygmwBlEvXiehDMcxiFYV10b+\/1oo6B098xZiJ2XJSaCa+FQRE9UsydvRHrJWRUF10YLnZfyU8pgy\/mQ6YUJVWYzneu\/WN2mwD9kQLIWHc=","accesskey":"ZQnAbZjCvNCjXY9rFSHtBct1XfkvOv5d6w9FLD5WPyddiDE584MbKL1na5X0Nao22bnsFRXu6bC8ZGO\/GJcaKw==","cachefile":"\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/","img":["\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_151344_70524638936633_869.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_151344_70524776255904_870.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_151344_70531041802257_869.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250801_151344_70531083771007_870.jpg"],"dstwidth":8000,"dstheight":4000,"isopt":0,"ismulti":1,"mutiluv":1}
15:14:43.423 D/StitchUtils [arch_disk_io_6, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:50)]: 拼接结束，拼接结果=0
15:17:09.513 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, Security type: -1, Supplicant state: COMPLETED, Wi-Fi standard: 4, RSSI: -5, Link speed: 65Mbps, Tx Link speed: 65Mbps, Max Supported Tx Link speed: 72Mbps, Rx Link speed: 65Mbps, Max Supported Rx Link speed: 72Mbps, Frequency: 2437MHz, Net ID: -1, Metered hint: false, score: 60, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1
15:17:17.687 E/VideoUtil [main, com.icatch.mobilecam.utils.VideoUtil.getVideoResolution(VideoUtil.java:2)]: args[0] = VideoUtils
args[1] = 不是视频：/storage/emulated/0/DCIM/IJOYER/20250801_151344_HDR.JPG

15:17:17.751 D/PanoramaPlayerActivity [main, com.ijoyer.camera.activity.PanoramaPlayerActivity$7.onSuccess(PanoramaPlayerActivity.java:4)]: PanoramaPlayerActivity HMS Image Vision API 初始化成功
15:17:24.617  [main, .refreshBatteryLevel(PreviewPresenter.java:4)]: 刷新电池电量：100
15:17:24.938  [main, .refreshBatteryLevel(PreviewPresenter.java:4)]: 刷新电池电量：100
15:17:41.028 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, Security type: -1, Supplicant state: COMPLETED, Wi-Fi standard: 4, RSSI: -5, Link speed: 65Mbps, Tx Link speed: 65Mbps, Max Supported Tx Link speed: 72Mbps, Rx Link speed: 65Mbps, Max Supported Rx Link speed: 72Mbps, Frequency: 2437MHz, Net ID: -1, Metered hint: false, score: 60, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1
15:17:54.034  [main, .refreshBatteryLevel(PreviewPresenter.java:4)]: 刷新电池电量：100
15:17:54.358  [main, .refreshBatteryLevel(PreviewPresenter.java:4)]: 刷新电池电量：100
15:17:58.848 D/PreviewActivity [main, com.icatch.mobilecam.ui.activity.PreviewActivity.onDestroy(PreviewActivity.java:4)]: PreviewActivity:正常销毁
15:17:59.860 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, Security type: -1, Supplicant state: COMPLETED, Wi-Fi standard: 4, RSSI: -5, Link speed: 65Mbps, Tx Link speed: 65Mbps, Max Supported Tx Link speed: 72Mbps, Rx Link speed: 65Mbps, Max Supported Rx Link speed: 72Mbps, Frequency: 2437MHz, Net ID: -1, Metered hint: false, score: 60, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1
15:17:59.861 D/MainActivity [main, com.ijoyer.camera.activity.MainActivity.goToNext(MainActivity.java:3)]: 进入：lastExitPreviewActivityTime=1460
15:17:59.862 W/MainActivity [main, com.ijoyer.camera.activity.MainActivity.goToNext(MainActivity.java:7)]: 距离上次退出预览界面时间太近，拦截掉
15:17:59.862 D/MainActivity [main, com.ijoyer.camera.activity.MainActivity.goToNext(MainActivity.java:8)]: 距离上次退出预览界面时间太近，拦截掉
15:18:06.711 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, Security type: -1, Supplicant state: COMPLETED, Wi-Fi standard: 4, RSSI: -5, Link speed: 65Mbps, Tx Link speed: 65Mbps, Max Supported Tx Link speed: 72Mbps, Rx Link speed: 65Mbps, Max Supported Rx Link speed: 72Mbps, Frequency: 2437MHz, Net ID: -1, Metered hint: false, score: 60, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1
15:18:06.713 D/MainActivity [main, com.ijoyer.camera.activity.MainActivity.goToNext(MainActivity.java:3)]: 进入：lastExitPreviewActivityTime=8311
15:18:08.301  [main, .refreshBatteryLevel(PreviewPresenter.java:4)]: 刷新电池电量：100
15:18:08.618  [main, .refreshBatteryLevel(PreviewPresenter.java:4)]: 刷新电池电量：100
15:18:31.148  [main, $PreviewHandler.handleMessage(PreviewPresenter.java:2)]: 收到相机发过来的事件：8
15:18:32.753 D/PreviewActivity [main, com.icatch.mobilecam.ui.activity.PreviewActivity.onDestroy(PreviewActivity.java:4)]: PreviewActivity:正常销毁
